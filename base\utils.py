import os
import codecs
import json
import logging
from datetime import date, datetime
from dotenv import load_dotenv
from uuid import uuid4

from base.litellm import LiteLLM
from queries import LogQuery
from .constants import SALE_METHODOLOGY
from .response import ErrorCode, ErrorMessage, ResponseType, return_response

load_dotenv()

logger = logging.getLogger()
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def generate_litellm_key_v2(hs_hub_id, litellm_team_id, max_budget=None):
    from base.litellm import LiteLLM

    import traceback
    try:
        lite_llm = LiteLLM()
        response = lite_llm.generate_key(
            user_id_str=str(hs_hub_id),
            litellm_team_id=litellm_team_id,
            max_budget=max_budget,
        )
        logger.info(response)
        logger.info(response["data"]["key"])
        return response["data"]["key"]
    except Exception as e:
        start_datetime = datetime.now()
        error = True
        err_msg = str(e)
        err_traceback = traceback.format_exc()
        body = {
            "hs_hub_id": hs_hub_id,
            "litellm_team_id": litellm_team_id,
            "max_budget": max_budget,
        }

        save_to_log(
            api="generate_litellm_key_v2",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=body,
            version="20241007-001"
        )
        raise e


def delete_litellm_keys(litellm_keys):
    success = False

    lite_llm = LiteLLM()
    response = lite_llm.delete_keys(litellm_keys)
    # print("delete_litellm_keys")
    # print(response)
    if not response["error"]:
        success = True
    else:
        raise Exception(response["message"])

    return success


def read_html_file(file_path):
    file = codecs.open(file_path, 'r', "utf-8")
    return file.read()


def validate_action_type(action_type, compared_action_type):
    if not action_type or action_type != compared_action_type:
        return return_response(ResponseType.ERROR, message="Invalid actionType")


def validate_app_id(app_id):
    if not app_id or str(app_id) != str(os.getenv("HUBSPOT_APP_ID")):
        return return_response(ResponseType.ERROR, message="Invalid appId")


def validate_sale_method(sale_method):
    if not sale_method or sale_method not in SALE_METHODOLOGY.keys():
        return return_response(ResponseType.ERROR, message=ErrorMessage.SALE_METHOD_INVALID, html=True)


def save_to_log(api, start_datetime, error, err_msg, err_traceback, input_data, version, err_code=None, real_err_msg=None):
    today_str = date.today().strftime("%y%m%d")
    random_str = str(uuid4().int)[:10]

    LogQuery().create_log({
        "id": int(f"{today_str}{random_str}"),
        "api": api,
        "err_code": err_code or ErrorCode.API_ERR,
        "error": error,
        "err_msg": err_msg,
        "real_err_msg": real_err_msg,
        "err_traceback": err_traceback,
        "input": json.dumps(input_data) if error else None,
        "executed_time": round((datetime.now() - start_datetime).microseconds * 0.000001, 3),
        "created_datetime": start_datetime,
        "version": version
    })
