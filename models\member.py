import os
from pynamodb.models import Model
from pynamodb.attributes import (
    UnicodeAttribute, UTCDateTimeAttribute, NumberAttribute, BooleanAttribute
)


class MemberModel(Model):
    class Meta:
        read_capacity_units = 1
        write_capacity_units = 1
        table_name = 'members'
        region = os.getenv('AWS_REGION_NAME')
    id = UnicodeAttribute(hash_key=True)
    hs_hub_id = NumberAttribute()
    hs_user_id = NumberAttribute()
    hs_user_email = UnicodeAttribute()
    full_name = UnicodeAttribute()
    is_super_admin = BooleanAttribute(default=False)
    litellm_key = UnicodeAttribute(null=True, default=None)
    created_datetime = UTCDateTimeAttribute()
    last_active_datetime = UTCDateTimeAttribute(null=True, default=None)
    is_active = BooleanAttribute(default=False)
    # reset data when subscription is paid
    is_reset_data = BooleanAttribute(default=False)
    # sale_method = UnicodeAttribute(null=True)
    # sale_method_description = UnicodeAttribute(null=True)
