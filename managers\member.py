import os
from datetime import datetime
from models import MemberModel
from queries import MemberQuery
from services.user import UserService


class MemberManager:
    def __init__(self, user_service: UserService):
        self.model = MemberModel
        self.model_query = MemberQuery
        self.user_service = user_service
        self.user = user_service.user

    def create_members_from_hubspot_data(self, hubspot_infos: list):
        """
        :param hubspot_infos:
            [
                {
                    'id': '68315455',
                    'email': '<EMAIL>',
                    'firstName': 'Kien',
                    'lastName': 'Hoang',
                    'roleIds': [],
                    'superAdmin': True
                }
            ]
        :return:
        """
        created_datetime = datetime.utcnow()
        member_data_list = []
        for hubspot_info in hubspot_infos:
            hs_user_id = int(hubspot_info["id"])
            member_data_list.append(self.model(
                id=f"{self.user.hs_hub_id}-{hs_user_id}",
                hs_hub_id=self.user.hs_hub_id,
                hs_user_id=hs_user_id,
                hs_user_email=hubspot_info["email"],
                full_name=hubspot_info["firstName"] + " " + hubspot_info["lastName"],
                is_super_admin=hubspot_info["superAdmin"],
                created_datetime=created_datetime,
                is_active=False,
            ))

        if len(member_data_list) > 0:
            self.model_query().bulk_create(member_data_list)

    def create_member(self, hs_user_id: int):
        self.user_service.validate_total_member(add_member=True, raise_exception=True)

        created_datetime = datetime.utcnow()
        hash_key = f"{self.user.hs_hub_id}-{hs_user_id}"

        data = dict(
            id=hash_key,
            hs_hub_id=self.user.hs_hub_id,
            hs_user_id=hs_user_id,
            hs_user_email=None,
            full_name=None,
            is_super_admin=False,
            created_datetime=created_datetime,
        )
        self.model_query().create(data)

        return self.get_member(hash_key)

    # Get a specific member by id (hash_key)
    def get_member(self, hash_key: str):
        return self.model_query().get_by_id(hash_key)

    def get_all_member(self):
        return self.model_query().get_list(hs_hub_id=self.user.hs_hub_id)

    def get_all_member_info_for_setting(self, current_member: MemberModel):
        accounts = []
        if current_member.is_super_admin is True:
            member_iterators = self.get_all_member()
            for member in member_iterators:
                # print(member)
                account = {
                    "accountId": member.hs_user_id,
                    "accountName": member.full_name,
                    "appId": os.getenv("HUBSPOT_APP_ID")
                }
                # print(account)
                accounts.append(account)
        else:
            account = {
                "accountId": current_member.hs_user_id,
                "accountName": current_member.full_name,
                "appId": os.getenv("HUBSPOT_APP_ID")
            }
            # print(account)
            accounts.append(account)
        # print(accounts)
        return accounts
