import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.response import (
    CustomException, ErrorMessage, ResponseType, return_response
)
from base.utils import validate_app_id, validate_sale_method, save_to_log
from managers import <PERSON><PERSON><PERSON><PERSON><PERSON>, MemberManager
from services import UserService, MemberService
import urllib.parse

# Load environment variables
load_dotenv()

logger = logging.getLogger("setting-sale-method-update")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()

    body = urllib.parse.parse_qs(event["body"])
    # Convert the values from lists to single values
    body = {k: v[0] for k, v in body.items()}

    try:
        logger.info("version: ********-001")
        logger.info(body)
        app_id = body.get("appId")
        # account_id = event["body"].get("accountId")
        portal_id = body.get("portalId")
        user_id = body.get("userId")
        # user_email = body.get("userEmail")
        sale_method = body.get("method")
        sale_method_description = body.get("sales_process")

        if response := validate_app_id(app_id): return response  # noqa)
        if response := validate_sale_method(sale_method): return response  # noqa)

        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        # get member from db
        member_manager = MemberManager(user_service)
        hash_key = f"{portal_id}-{user_id}"
        member = member_manager.get_member(hash_key)
        logger.info(member)
        member_service = MemberService(member, user_service)
        member_service.validate_member(raise_exception=True)

        user_service.update_sale_method(sale_method, sale_method_description)

        return return_response(ResponseType.SUCCESS, html=True)
    except CustomException as ce:
        error = True
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        err_msg = ErrorMessage.COMMON.format(err_code)
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg, html=True)
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(body)

        return return_response(ResponseType.EXCEPTION, html=True)
    finally:
        save_to_log(
            api="setting_sale_method_update",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=body,
            version="********-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )

