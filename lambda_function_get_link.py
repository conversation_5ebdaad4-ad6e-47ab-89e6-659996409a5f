import json
import logging
import os
import stripe
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.response import return_response, ResponseType
from base.utils import save_to_log

# Load environment variables
load_dotenv()

logger = logging.getLogger("get-link")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


# Function to map products and prices
def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()

    body = json.loads(event["body"])

    try:
        price_id = body['price_id']

        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")
        # Fetch all products
        session = stripe.checkout.Session.create(
            billing_address_collection='auto',
            line_items=[
                {
                    'price': price_id,
                    'quantity': 1,
                },
            ],
            allow_promotion_codes=True,
            mode='subscription',
            success_url=os.getenv("STRIPE_SUCCESS_CALLBACK_URI"),
            cancel_url=os.getenv("STRIPE_CANCEL_CALLBACK_URI"),
        )

        return {
            'statusCode': 200,
            'headers': {
                'Content-Type': 'application/json',
                'Access-Control-Allow-Origin': '*',  # Allow all origins or specify your origin
                'Access-Control-Allow-Headers': 'Content-Type',
                'Access-Control-Allow-Methods': 'OPTIONS,POST,GET'
            },
            'body': json.dumps({'url': session.url})
        }
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(body)

        return return_response(ResponseType.EXCEPTION)
    finally:
        save_to_log(
            api="get_link",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=body,
            version="20241028-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
