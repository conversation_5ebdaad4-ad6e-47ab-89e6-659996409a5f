from models.member import MemberModel
from .base import BaseQuery


class MemberQuery(BaseQuery):
    model = MemberModel

    def get_list(self, **kwargs):
        hs_hub_id = kwargs.get("hs_hub_id")
        condition = None

        if hs_hub_id:
            condition &= self.model.hs_hub_id == hs_hub_id
        # print(1111)
        # print(condition)
        return self.model.scan(filter_condition=condition)
