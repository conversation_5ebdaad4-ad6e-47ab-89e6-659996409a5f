import stripe
import logging
from datetime import datetime
from pytz import timezone
from dotenv import load_dotenv

# Create a logger
logger = logging.getLogger()
logger.setLevel(logging.INFO)  # Set the log level

load_dotenv()
stripe.api_key = "sk_test_51PSgVBBuk4DyGINqSGOZbDSoSvoOYM6gang3HWeQgFxHmz8hEB8HswKRs5DCEdingV9pRm7zZJasbC1jol7gzYFn00wyE3LgjP"


def get_subscription_end_time(subscription_id):
    try:
        # Retrieve the subscription details from Stripe
        subscription = stripe.Subscription.retrieve(subscription_id)
        
        # Extract the subscription end time
        current_period_end = subscription.get('current_period_end')
        
        if current_period_end:
            # Convert the timestamp to a datetime object
            end_time = datetime.fromtimestamp(current_period_end, tz=timezone('UTC'))
            return end_time
        else:
            return None
    except stripe.error.StripeError as e:
        print(f"Error retrieving subscription: {e}")
        return None


print(get_subscription_end_time("sub_1PvemQBuk4DyGINqrdnGRT3c"))
