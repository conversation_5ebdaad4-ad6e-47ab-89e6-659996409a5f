import json
import logging
import os
import stripe
import traceback
from datetime import datetime
from dotenv import load_dotenv
from pytz import timezone

from base.constants import MAX_BUDGET
from base.hubspot import <PERSON><PERSON>pot
from base.litellm import LiteLLM
from base.response import ErrorCode, ErrorMessage, return_response, ResponseType
from base.utils import save_to_log
from managers import MemberManager, UserManager
from services import UserService


load_dotenv()

logger = logging.getLogger("oauth-callback")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def get_subscription_end_time(subscription_id):
    try:
        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

        # Retrieve the subscription details from Stripe
        subscription = stripe.Subscription.retrieve(subscription_id)

        # Extract quantity (as max_member)
        max_member = subscription.get("quantity")
        # Extract the subscription end time
        current_period_end = subscription.get("current_period_end")
        if current_period_end:
            # Convert the timestamp to a datetime object
            end_time = datetime.fromtimestamp(current_period_end, tz=timezone('UTC'))
            return end_time, max_member
        else:
            return None, None
    except stripe.error.StripeError as e:
        logger.error(f"Error retrieving subscription: {e}")
        return None, None


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()

    query_params = event["queryStringParameters"]
    try:
        logger.info("20241116-001")
        code = query_params["code"]
        subscription_id = query_params["state"]

        user_manager = UserManager()

        # get access token
        redirect_uri = os.getenv("OAUTH_CALLBACK_URI")
        hubspot = Hubspot()
        response = hubspot.get_access_token(code, redirect_uri)
        # print(type(response))
        if response["error"]:
            error = True
            err_code = ErrorCode.OTHER
            err_msg = ErrorMessage.COMMON.format(err_code)
            real_err_msg = ErrorMessage.HUBSPOT_ACCESS_TOKEN

            return return_response(ResponseType.ERROR, message=err_msg,
                                   html=True)

        response_data = response["data"]
        access_token = response_data["access_token"]
        refresh_token = response_data["refresh_token"]
        hs_access_token_expired_time = None

        # get user info from hubspot
        hubspot = Hubspot(access_token, refresh_token)
        response = hubspot.get_info_by_access_token()
        logger.info(response)
        if response["error"]:
            error = True
            err_code = ErrorCode.OTHER
            err_msg = ErrorMessage.COMMON.format(err_code)
            real_err_msg = ErrorMessage.HUBSPOT_INFO_FROM_ACCESS_TOKEN

            return return_response(ResponseType.ERROR, message=err_msg, html=True)

        response_data = response["data"]
        hs_hub_id = response_data["hub_id"]

        # get user from db
        user = user_manager.get_user(hs_hub_id)

        # create new user
        if user is None:
            end_paid_datetime, max_member = get_subscription_end_time(subscription_id)
            lite_llm = LiteLLM()
            response = lite_llm.create_team(
                team_id=str(hs_hub_id),
                max_budget=max_member * MAX_BUDGET
            )
            litellm_team_id = None
            if response["error"]:
                if response["message"] and "already exists" in response["message"]:
                    litellm_team_id = str(hs_hub_id)
                else:
                    error = True
                    err_code = ErrorCode.OTHER
                    err_msg = ErrorMessage.COMMON.format(err_code)
                    real_err_msg = ErrorMessage.LITELLM_TEAM_ID_NOT_FOUND

                    return return_response(ResponseType.ERROR, message=err_msg, html=True)

            else:
                litellm_team_id = response["data"]["team_id"]

            user_data = {
                "hs_hub_id": hs_hub_id,
                "hs_hub_domain": response_data["hub_domain"],
                "hs_email": response_data["user"],
                "hs_access_token": access_token,
                "hs_refresh_token": refresh_token,
                "hs_access_token_expired_time": hs_access_token_expired_time,
                "subscription_id": subscription_id,
                "max_member": max_member,
                "end_paid_datetime": end_paid_datetime,
                "litellm_team_id": litellm_team_id,
            }

            user = user_manager.create_user(**user_data)

        logger.info(user)
        user_service = UserService(user)
        member_manager = MemberManager(user_service)

        # create members of user
        # get from hubspot
        response = hubspot.get_user_list()
        logger.info(response)
        if response["error"]:
            error = True
            err_code = ErrorCode.OTHER
            err_msg = ErrorMessage.COMMON.format(err_code)
            real_err_msg = ErrorMessage.HUBSPOT_USER_LIST

            return return_response(ResponseType.ERROR, message=err_msg, html=True)

        response_data = response["data"]["results"]

        # store members into db
        member_manager.create_members_from_hubspot_data(response_data)

        # validate user
        if user_service.validate_end_paid_datetime() is False:
            return return_response(location=os.getenv("PRICING_URI"))

        return return_response(location=os.getenv("HUBSPOT_SETTING_URI").format(hs_hub_id=user.hs_hub_id))
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(query_params)

        return return_response(ResponseType.EXCEPTION, html=True)
    finally:
        save_to_log(
            api="oauth_callback",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=query_params,
            version="20241116-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
