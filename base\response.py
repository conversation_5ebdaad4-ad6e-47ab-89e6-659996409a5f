import codecs
import json


class ErrorCode:
    OTHER = "GENERROR"
    USER_NOT_FOUND = "USERPRIV"
    USER_EXCEEDED = "USEREXEED"
    MEMBER_NOT_FOUND = "MEMBERPRIV"
    LITELLM_BUDGET_EXCEEDED = "BUDGEXE"
    API_ERR = "GATEERROR"
    OPEN_AI_FAIL = "OPENERROR"


class ErrorMessage:
    # common
    COMMON = "An Issue has occurred with RevBot: Code {}; please contact your HubSpot administrator, or reach out to your RevBot contact"
    # user
    USER_NOT_FOUND = "Account not found"
    USER_EXCEEDED = "Number of user exceeded"
    # hubspot
    HUBSPOT_COMMON = "Error contacting hubspot apis"
    HUBSPOT_ACCESS_TOKEN = "Fail to get access token from hubspot api"
    HUBSPOT_INFO_FROM_ACCESS_TOKEN = "Fail to get info from access token from hubspot api"
    HUBSPOT_USER_LIST = "Fail to get user list from hubspot api"  # aka member list in DB
    HUBSPOT_USER_DETAIL = "Fail to get user detail from hubspot api"  # aka member in DB
    # member
    MEMBER_NOT_FOUND = "You do not have a RevBot license assigned, please contact your HubSpot administrator for more information."
    # litellm
    LITELLM_COMMON = "Error contacting litellm apis"
    LITELLM_KEY_NOT_FOUND = "Litellm key not found"
    LITELLM_TEAM_ID_NOT_FOUND = "Litellm team id not found"
    LITELLM_TEAM_MAX_BUDGET_NOT_SET = "Litellm team not set max budget"
    LITELLM_TEAM_MAX_BUDGET_UPDATE_FAIL = "Litellm team fail to update max budget"
    LITELLM_KEY_GENERATE_FAIL = "Litellm key fail to generate"
    LITELLM_BUDGET_EXCEEDED = "Litellm budget exceeded"
    # open ai
    OPEN_AI_FAIL = "Error contacting open-ai apis"
    # other
    SALE_METHOD_INVALID = "Invalid sale method"


class CustomException(Exception):
    def __init__(self, message=ErrorMessage.COMMON.format(ErrorCode.OTHER), err_code=ErrorCode.OTHER, real_err_mess=None):
        super().__init__(message)
        self.message = message
        self.err_code = err_code
        self.real_err_mess = real_err_mess

    def get_err_code(self):
        return self.err_code

    def get_real_err_mess(self):
        return self.real_err_mess


class LiteLLMException(CustomException):
    pass


class UserException(CustomException):
    pass


class MemberException(CustomException):
    pass


class ResponseType:
    SUCCESS = "success"
    ERROR = "error"
    EXCEPTION = "exception"


class HTTPStatusCode:
    HTTP_200_OK = 200
    # HTTP_201_CREATED = 201
    HTTP_302_FOUND = 302
    HTTP_400_BAD_REQUEST = 400
    # HTTP_401_UNAUTHORIZED = 401
    # HTTP_403_FORBIDDEN = 403
    HTTP_404_NOT_FOUND = 404
    # HTTP_405_METHOD_NOT_ALLOWED = 405
    HTTP_500_INTERNAL_SERVER_ERROR = 500
    # HTTP_502_BAD_GATEWAY = 502
    # HTTP_503_SERVICE_UNAVAILABLE = 503
    # HTTP_504_GATEWAY_TIMEOUT = 504


def read_html_file(file_path):
    file = codecs.open(file_path, 'r', "utf-8")
    return file.read()


def _return_html_response(response_type=None, message=None):
    if response_type == ResponseType.SUCCESS:
        file_name = ResponseType.SUCCESS
    else:
        file_name = ResponseType.ERROR

    html = read_html_file(f"templates/{file_name}.html")
    if message:
        html = html.replace(
            "Your request has been processed completely!",
            message
        )

    return {
        "statusCode": HTTPStatusCode.HTTP_200_OK,
        "body": html,
        "headers": {
            "Content-Type": "text/html",
        }
    }


def _return_json_response(
    response_type=None,
    status_code=None,
    data=None,
    message=None,
):
    if message:
        data = {
            "error": False if response_type == ResponseType.SUCCESS else True,
            "message": message
        }

    return {
        "statusCode": status_code,
        "body": json.dumps(data),
    }


def _return_forward_response(location=None):
    """
    Exp:
    {
        "statusCode": 302,
        "headers": {
            "Location": "https://scovy.io/price-table/"
        }
    }
    """
    return {
        "statusCode": HTTPStatusCode.HTTP_302_FOUND,
        "headers": {
            "Location": location
        }
    }


def return_response(
    response_type=ResponseType.SUCCESS,
    status_code=None,
    data=None,
    message=None,
    location=None,
    html=False,
):
    if status_code is None:
        if response_type == ResponseType.SUCCESS:
            status_code = HTTPStatusCode.HTTP_200_OK
        elif response_type == ResponseType.ERROR:
            status_code = HTTPStatusCode.HTTP_400_BAD_REQUEST
        elif response_type == ResponseType.EXCEPTION:
            status_code = HTTPStatusCode.HTTP_500_INTERNAL_SERVER_ERROR
        else:
            raise Exception("Invalid response type")
    if response_type == ResponseType.EXCEPTION:
        if message is None:
            message = ErrorMessage.COMMON.format(ErrorCode.OTHER)

    if location:
        return _return_forward_response(location)
    else:
        if html:
            return _return_html_response(response_type, message)
        else:
            return _return_json_response(response_type, status_code, data, message)
