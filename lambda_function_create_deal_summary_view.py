import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.response import ResponseType, return_response
from base.utils import read_html_file, save_to_log

# Load environment variables
load_dotenv()

logger = logging.getLogger("create-deal-summary-view")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()

    try:
        html = read_html_file("templates/create_deal_summary.html")
        html = html.replace(
            "create-deal-summary-link",
            "{}/create-deal-summary".format(os.getenv("PROCESS_INTERACTION_URI"))
        )
        # logger.info(html)
        return {
            "statusCode": 200,
            "body": html,
            "headers": {
                "Content-Type": "text/html",
            }
        }
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))

        return return_response(ResponseType.EXCEPTION, html=True)
    finally:
        save_to_log(
            api="create_deal_summary_view",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=None,
            version="20241028-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
