<html>
    <head>
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@200..700&display=swap" rel="stylesheet">
        <script
            src="https://code.jquery.com/jquery-3.7.1.slim.js"
            integrity="sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="
            crossorigin="anonymous"
        ></script>
        <script
            type="text/javascript"
            src="https://ajax.googleapis.com/ajax/libs/jqueryui/1.11.1/jquery-ui.min.js"
        ></script>
        <style>
            img {
                float: left;
                margin-right: 20px;
            }
            .tg {
                border-collapse: collapse;
                border-spacing: 0;
                background: #f4f4f4;
                border-radius: 12px;
                padding: 20px;
            }
            .tg td {
                border-color: white;
                border-style: none;
                border-width: 1px;
                font-family: Arial, sans-serif;
                font-size: 16px;
                overflow: hidden;
                padding: 10px 5px;
                word-break: normal;
            }
            .tg th {
                border-color: white;
                border-style: none;
                border-width: 1px;
                font-family: Arial, sans-serif;
                font-size: 16px;
                font-weight: normal;
                overflow: hidden;
                padding: 10px 5px;
                word-break: normal;
            }
            .tg .tg-0lax {
                text-align: left;
                vertical-align: top;
            }
            .button-container {
                text-align: center;
            }
            .button-15 {
                background-image: linear-gradient(#42a1ec, #0070c9);
                border: 1px solid #0077cc;
                border-radius: 4px;
                box-sizing: border-box;
                color: #ffffff;
                cursor: pointer;
                direction: ltr;
                display: block;
                font-family: "SF Pro Text", "SF Pro Icons", "AOS Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
                font-size: 17px;
                font-weight: 400;
                letter-spacing: -0.022em;
                line-height: 1.47059;
                min-width: 30px;
                overflow: visible;
                padding: 4px 15px;
                text-align: center;
                vertical-align: baseline;
                user-select: none;
                -webkit-user-select: none;
                touch-action: manipulation;
                white-space: nowrap;
                margin: 5px;
            }
            .button-15:disabled {
                cursor: default;
                opacity: 0.3;
            }
            .button-15:hover {
                background-image: linear-gradient(#51a9ee, #147bcd);
                border-color: #1482d0;
                text-decoration: none;
            }
            .button-15:active {
                background-image: linear-gradient(#3d94d9, #0067b9);
                border-color: #006dbc;
                outline: none;
            }
            .button-15:focus {
                box-shadow: rgba(131, 192, 253, 0.5) 0 0 0 3px;
                outline: none;
            }
            .button-16 {
                background-image: linear-gradient(#42a1ec, #0070c9);
                border: 1px solid #0077cc;
                border-radius: 4px;
                box-sizing: border-box;
                color: #ffffff;
                cursor: pointer;
                direction: ltr;
                display: block;
                font-family: "SF Pro Text", "SF Pro Icons", "AOS Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
                font-size: 17px;
                font-weight: 400;
                letter-spacing: -0.022em;
                line-height: 1.47059;
                min-width: 30px;
                overflow: visible;
                padding: 4px 15px;
                text-align: center;
                vertical-align: baseline;
                user-select: none;
                -webkit-user-select: none;
                touch-action: manipulation;
                white-space: nowrap;
                margin: 5px;
            }
            .button-16:disabled {
                cursor: default;
                opacity: 0.3;
            }
            .button-16:hover {
                background-image: linear-gradient(#51a9ee, #147bcd);
                border-color: #1482d0;
                text-decoration: none;
            }
            .button-16:active {
                background-image: linear-gradient(#3d94d9, #0067b9);
                border-color: #006dbc;
                outline: none;
            }
            .button-16:focus {
                box-shadow: rgba(131, 192, 253, 0.5) 0 0 0 3px;
                outline: none;
            }

            #processing-message {
                padding-top: 20px;
            }
            /* HTML: <div class="loader"></div> */
            .loader {
              margin: auto;
              width: 50px;
              --b: 8px;
              aspect-ratio: 1;
              border-radius: 50%;
              background: #514b82;
              -webkit-mask:
                repeating-conic-gradient(#0000 0deg,#000 1deg 70deg,#0000 71deg 90deg),
                radial-gradient(farthest-side,#0000 calc(100% - var(--b) - 1px),#000 calc(100% - var(--b)));
              -webkit-mask-composite: destination-in;
                      mask-composite: intersect;
              animation: l5 1s infinite;
            }
            @keyframes l5 {to{transform: rotate(.5turn)}}
        </style>
        <script>
            $(document).ready(function () {
                const portalId =  new URLSearchParams(window.location.search).get('portal_id');
                console.log(portalId);
                document.getElementById("portalId").value = portalId;
                const userId =  new URLSearchParams(window.location.search).get('user_id');
                console.log(userId);
                document.getElementById("userId").value = userId;
                const userEmail =  new URLSearchParams(window.location.search).get('user_email');
                console.log(userEmail);
                document.getElementById("userEmail").value = userEmail;
                const hubspot_owner_id =  new URLSearchParams(window.location.search).get('hubspot_owner_id');
                console.log(hubspot_owner_id);
                document.getElementById("hubspot_owner_id").value = hubspot_owner_id;
                const hs_object_id =  new URLSearchParams(window.location.search).get('hs_object_id');
                console.log(hs_object_id);
                document.getElementById("hs_object_id").value = hs_object_id;

                // document.querySelector("#dealForm").addEventListener("submit", function(e){
                //     window.parent.postMessage(JSON.stringify({"action": "DONE"}), "*");
                // });

                $('#processing-message').hide();
                $('.loader').hide();

                $("#deselect_all").prop("disabled", true).css("opacity", 0.5);

                $("#select_all").click(function () {
                    $(":checkbox").each(function () {
                        this.checked = true;
                    });
                    $("#select_all").prop("disabled", true).css("opacity", 0.5);
                    $("#deselect_all")
                        .prop("disabled", false)
                        .css("opacity", 1);
                });

                $("#deselect_all").click(function () {
                    $(":checkbox").each(function () {
                        this.checked = false;
                    });
                    $("#deselect_all")
                        .prop("disabled", true)
                        .css("opacity", 0.5);
                    $("#select_all").prop("disabled", false).css("opacity", 1);
                });

                $('#submit').click(function() {
                    $('#checkboxes').hide();
                    $('.button-container').hide();
                    $('#processing-message').show();
                    $('.loader').show();
                });
            });
        </script>
    </head>

    <body>
        <div
            style="
                box-shadow: 0px 0px 4px #dddddd;
                border-top: 0px;
                border-color: #dddddd;
                border-style: solid;
                border-left: 0px;
                border-right: 0px;
                border-bottom: 0px;
                width: 498px;
                display: inline-block;
                padding-left: 34px;
                height: 351px;
                padding-top: 22px;"
        >
	        <img src="https://revbot.s3.ap-southeast-1.amazonaws.com/RevBot_Icon.png" alt="RevBot Icon" width="200"
                 style="width: 110px;
                        margin-top: 12px;
                        margin-left: 7px;"
            >
	        <h2 style="font-family: 'Oswald', sans-serif;
                      font-optical-sizing: auto;
                      font-weight: 800;
                      font-style: normal;"
            >REVBOT: AI Powered Sales Engine</h2>

            <div class="button-container">
                <button
                    class="button-15"
                    role="button"
                    type="button"
                    id="select_all"
                >
                    Select All</button
                ><button
                    class="button-15"
                    role="button"
                    type="button"
                    id="deselect_all" style="position: relative; top: -40px; right: -240px;"
                >
                    Deselect All
                </button>
            </div>
            <br>
            <form action="https://9a67-171-224-181-194.ngrok-free.app/create-deal-summary" id="dealForm" method="POST">
                <h3 id="processing-message">We are consuming your deal data now, RevBot will complete analysis shortly</h3>
                <div class="loader" ></div>
                <div id="checkboxes">
                    <table class="tg" style="position:relative; top: -39px;"><tbody>
                        <tbody>
                            <input type="hidden" id="portalId" name="portalId" value="">
                            <input type="hidden" id="userId" name="userId" value="">
                            <input type="hidden" id="userEmail" name="userEmail" value="">
                            <input type="hidden" id="hubspot_owner_id" name="hubspot_owner_id" value="">
                            <input type="hidden" id="hs_object_id" name="hs_object_id" value="">
                            <tr>
                                <td class="tg-0lax">
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="emails"
                                    />Include Emails<br />
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="calls"
                                    />Include Calls<br />
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="sms"
                                    />Include SMS Messages<br />
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="linkedin"
                                    />Include LinkedIn Messages<br />
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="notes"
                                    />Include Notes<br />
                                </td>
                                <td class="tg-0lax">
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="meetings"
                                    />Include Meetings<br />
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="tasks"
                                    />Include Tasks<br />
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="forms"
                                    />Include Form Submissions<br />
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="whatsapp"
                                    />Include WhatsApp Messages<br />
                                    <input
                                        type="checkbox"
                                        name="activities"
                                        value="chat"
                                    />Include Live Chats<br />
                                </td>
                            </tr>
                            <tr>
                                <td class="tg-0lax">
                                    <input
                                        class="button-15"
                                        role="button"
                                        type="submit"
                                        value="Run RevBot"
                                         id="submit"
                                    />
                                </td>
                                <td class="tg-0lax"></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>
    </body>
</html>
