img {
    float: left;
    margin-right: 20px;
}

.tg {
    border-collapse: collapse;
    border-spacing: 0;
    background: #f4f4f4;
    border-radius: 12px;
    padding: 20px;
}
.tg td {
    border-color: white;
    border-style: none;
    border-width: 1px;
    font-family: Arial, sans-serif;
    font-size: 16px;
    overflow: hidden;
    padding: 10px 5px;
    word-break: normal;
}
.tg th {
    border-color: white;
    border-style: none;
    border-width: 1px;
    font-family: Arial, sans-serif;
    font-size: 16px;
    font-weight: normal;
    overflow: hidden;
    padding: 10px 5px;
    word-break: normal;
}
.tg .tg-0lax {
    text-align: left;
    vertical-align: top;
}

.button-container {
    text-align: center;
}

.button-15 {
    background-image: linear-gradient(#42a1ec, #0070c9);
    border: 1px solid #0077cc;
    border-radius: 4px;
    box-sizing: border-box;
    color: #ffffff;
    cursor: pointer;
    direction: ltr;
    display: block;
    font-family: "SF Pro Text", "SF Pro Icons", "AOS Icons", "Helvetica Neue",
        Helvetica, Arial, sans-serif;
    font-size: 17px;
    font-weight: 400;
    letter-spacing: -0.022em;
    line-height: 1.47059;
    min-width: 30px;
    overflow: visible;
    padding: 4px 15px;
    text-align: center;
    vertical-align: baseline;
    user-select: none;
    -webkit-user-select: none;
    touch-action: manipulation;
    white-space: nowrap;
    margin: 5px;
}

.button-15:disabled {
    cursor: default;
    opacity: 0.3;
}

.button-15:hover {
    background-image: linear-gradient(#51a9ee, #147bcd);
    border-color: #1482d0;
    text-decoration: none;
}

.button-15:active {
    background-image: linear-gradient(#3d94d9, #0067b9);
    border-color: #006dbc;
    outline: none;
}

.button-15:focus {
    box-shadow: rgba(131, 192, 253, 0.5) 0 0 0 3px;
    outline: none;
}
