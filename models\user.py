import os
from pynamodb.models import Model
from pynamodb.attributes import (
    UnicodeAttribute, UTCDateTimeAttribute, NumberAttribute, BooleanAttribute,
    ListAttribute, MapAttribute
)


class UserDetails(MapAttribute):
    hs_user_id = NumberAttribute()
    hs_user_email = UnicodeAttribute(null=True)


class UserModel(Model):
    class Meta:
        read_capacity_units = 1
        write_capacity_units = 1
        table_name = 'users'
        region = os.getenv('AWS_REGION_NAME')
    hs_hub_id = NumberAttribute(hash_key=True)
    hs_hub_domain = UnicodeAttribute()
    hs_email = UnicodeAttribute(null=True)
    hs_access_token = UnicodeAttribute()
    hs_refresh_token = UnicodeAttribute()
    litellm_team_id = UnicodeAttribute(null=True)
    hs_access_token_expired_time = UnicodeAttribute(null=True)
    subscription_id = UnicodeAttribute(null=True)
    created_datetime = UTCDateTimeAttribute(null=True)
    end_paid_datetime = UTCDateTimeAttribute(null=True)
    sale_method = UnicodeAttribute(null=True)
    sale_method_description = UnicodeAttribute(null=True)
    total_member = NumberAttribute(default=0)
    max_member = NumberAttribute(default=0)
    is_active = BooleanAttribute(default=True)
    # reset data when subscription is paid
    is_reset_data = BooleanAttribute(default=False)
