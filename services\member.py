from datetime import datetime

from base.constants import MAX_BUDGET
from base.litellm import LiteLLM
from base.utils import generate_litellm_key_v2
from base.response import (
    ErrorMessage, ErrorCode, ResponseType, MemberException, CustomException,
    LiteLLMException, return_response
)
from models import MemberModel
from queries import MemberQuery
from .user import UserService


class MemberService:
    def __init__(self, member: MemberModel, user_service: UserService):
        self.member = member
        self.user_service = user_service
        self.user = self.user_service.user

    def validate_member(self, raise_exception=False):
        is_valid = not (self.member is None or self.member.is_active is False)
        if raise_exception and not is_valid:
            raise MemberException(message=ErrorMessage.MEMBER_NOT_FOUND, err_code=ErrorCode.MEMBER_NOT_FOUND)

        return is_valid

    def validate_member_settings(self, raise_exception=False):
        is_valid = not (self.member is None)
        if raise_exception and not is_valid:
            raise MemberException(message=ErrorMessage.MEMBER_NOT_FOUND, err_code=ErrorCode.MEMBER_NOT_FOUND)

        return is_valid

    def validate_budget_from_litellm(self, raise_exception=False):
        is_valid = True

        try:
            lite_llm = LiteLLM()
            response = lite_llm.get_key_info_v2(self.member.litellm_key)
            print(response)
            key_info = response["data"]["info"][0]
            # print(key_info)
            max_budget = key_info["max_budget"]
            if max_budget and max_budget <= key_info["spend"]:
                # print('vao day')
                is_valid = False
        except Exception as e:
            if issubclass(type(e), CustomException):
                raise e
            else:
                raise MemberException()

        if is_valid is False and raise_exception:
            raise LiteLLMException(
                ErrorMessage.LITELLM_BUDGET_EXCEEDED,
                err_code=ErrorCode.LITELLM_BUDGET_EXCEEDED
            )

        return is_valid

    def update_is_active(self, is_active: bool):
        member_data = {
            "is_active": is_active
        }
        MemberQuery().update_by_id(self.member.id, member_data)

    def update_is_super_admin(self, is_super_admin: bool):
        member_data = {
            "is_super_admin": is_super_admin
        }
        MemberQuery().update_by_id(self.member.id, member_data)

    def delete_litellm_key_from_db(self):
        member_data = {
            "litellm_key": None,
        }
        # print(999)
        # print(member_data)
        MemberQuery().update_by_id(self.member.id, member_data)

    def add_litellm_key(self, raise_exception=False):
        self.user_service.validate_total_member(add_member=True, raise_exception=True)

        litellm_key = generate_litellm_key_v2(
            self.member.id,
            litellm_team_id=self.user.litellm_team_id,
            max_budget=MAX_BUDGET,
        )
        if litellm_key:
            self.member.litellm_key = litellm_key

            updated_data = {
                "litellm_key": litellm_key,
                "last_active_datetime": datetime.utcnow(),
            }
            MemberQuery().update_by_id(self.member.id, updated_data)
        else:
            if raise_exception:
                raise LiteLLMException(
                    ErrorMessage.COMMON,
                    err_code=ErrorCode.OTHER,
                    real_err_mess=ErrorMessage.LITELLM_KEY_GENERATE_FAIL
                )

        return litellm_key

    def delete_litellm_key_from_litellm(self, raise_exception=False):
        success = False
        lite_llm = LiteLLM()
        response = lite_llm.delete_key(self.member.litellm_key)
        # print(response)
        if not response["error"]:
            success = True
        else:
            if raise_exception:
                raise LiteLLMException(
                    ErrorMessage.COMMON,
                    err_code=ErrorCode.OTHER,
                    real_err_mess=response["message"]
                )

        return success

    def update_max_budget_to_litellm(self, max_budget: int):
        success = False
        lite_llm = LiteLLM()
        response = lite_llm.update_key(self.member.litellm_key, max_budget=max_budget)
        # print(response)
        if not response["error"]:
            updated_data = {
                "last_active_datetime": datetime.utcnow(),
            }
            MemberQuery().update_by_id(self.member.id, updated_data)
            success = True
        else:
            raise LiteLLMException(
                ErrorMessage.COMMON,
                err_code=ErrorCode.OTHER,
                real_err_mess=response["message"]
            )

        return success

    # def reset_data(self):
    #     updated_data = {
    #         "is_reset_data": True
    #     }
    #     if self.member.litellm_key:
    #         success = self.delete_litellm_key()
    #
    #     MemberQuery().update_by_id(self.member.id, updated_data)
