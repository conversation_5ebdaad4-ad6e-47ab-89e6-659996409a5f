from datetime import datetime
from models import UserModel
from queries import UserQuery


class UserManager:
    def __init__(self):
        self.members = {}
        self.model = UserModel
        self.model_query = UserQuery

    # Create a new user
    def create_user(
        self,
        hs_hub_id,
        hs_access_token,
        hs_refresh_token,
        hs_access_token_expired_time,
        max_member,
        subscription_id,
        end_paid_datetime,
        hs_hub_domain,
        hs_email,
        litellm_team_id,
    ):
        user_data = {
            "hs_hub_id": hs_hub_id,
            "hs_hub_domain": hs_hub_domain,
            "hs_email": hs_email,
            "hs_access_token": hs_access_token,
            "hs_refresh_token": hs_refresh_token,
            "hs_access_token_expired_time": hs_access_token_expired_time,
            "subscription_id": subscription_id,
            "max_member": max_member,
            "total_member": 0,
            "is_active": True,
            "is_reset_data": False,
            "created_datetime": datetime.utcnow(),
            "end_paid_datetime": end_paid_datetime,
            "litellm_team_id": litellm_team_id,
        }

        self.model_query().create(user_data)

        return self.get_user(hs_hub_id)

    # Get a specific user by ID
    def get_user(self, hs_hub_id):
        return self.model_query().get_by_id(hs_hub_id)

    def get_user_by_subscription_id(self, subscription_id):
        return self.model_query().get_by_subscription_id(subscription_id)
