from models.log import LogModel


class LogQuery(object):
    def get_logs(self):
        return LogModel.scan()

    def get_log(self, hs_hub_id):
        return LogModel.query(hs_hub_id)

    def create_log(self, log_data):
        log_data_saved = LogModel(**log_data).save()
        return log_data_saved

    def update_log(self, hs_hub_id, updated_data):
        log = LogModel.get(hs_hub_id)
        for key, value in updated_data.items():
            setattr(log, key, value)
        log.save()
