class BaseQuery(object):
    model = None
    hash_key_field = "id"

    def __init__(self):
        if self.model is None:
            raise ValueError('Model must be provided')

    def create(self, data):
        self.model(**data).save()

    def bulk_create(self, data_list: list):
        with self.model.batch_write() as batch:
            for data in data_list:
                batch.save(data)

    def update_by_id(self, model_id, updated_data):
        obj = self.model.get(model_id)
        for key, value in updated_data.items():
            setattr(obj, key, value)
        obj.save()

        return obj

    def get_by_id(self, model_id):
        try:
            return list(self.model.query(model_id))[0]
        except:
            return None
