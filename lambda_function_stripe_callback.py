import logging
import os
import stripe
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.response import (
    ErrorMessage, ErrorCode, return_response, ResponseType, HTTPStatusCode
)
from base.utils import save_to_log

# Load environment variables
load_dotenv()

logger = logging.getLogger("stripe-callback")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()
    query_params = event["queryStringParameters"]

    # Retrieve the session ID from the query string parameters
    session_id = query_params.get("session_id")
    logger.info(session_id)
    
    try:
        if not session_id:
            err_code = ErrorCode.OTHER
            err_msg = ErrorMessage.COMMON.format(err_code)
            real_err_msg = "session_id is required"

            return return_response(
                ResponseType.ERROR,
                message=err_msg,
                html=True
            )

        stripe.api_key = os.getenv("STRIPE_SECRET_KEY")

        # Retrieve the session details from Stripe
        session = stripe.checkout.Session.retrieve(session_id)
        
        # Extract the subscription ID from the session details
        subscription_id = session.get('subscription')
        
        if not subscription_id:
            err_code = ErrorCode.OTHER
            err_msg = ErrorMessage.COMMON.format(err_code)
            real_err_msg = "Subscription ID not found in session"

            return return_response(
                ResponseType.ERROR,
                status_code=HTTPStatusCode.HTTP_404_NOT_FOUND,
                message=err_msg,
                html=True
            )

        return return_response(
            ResponseType.SUCCESS,
            location=os.getenv("HUBSPOT_OAUTH_AUTHORIZE_URI").format(
                hubspot_client_id=os.getenv("HUBSPOT_CLIENT_ID"),
                oauth_callback_uri=os.getenv("OAUTH_CALLBACK_URI"),
                subscription_id=subscription_id,
            )
        )
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(query_params)

        return return_response(ResponseType.EXCEPTION, html=True)
    finally:
        save_to_log(
            api="stripe_callback",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=query_params,
            version="20241028-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )

# Example usage
# if __name__ == "__main__":
#     event = {
#         "queryStringParameters": {
#             "session_id": "cs_test_a1q9eaKyuw5XUYsRJUTHnh5ApCrtxUrHwea4hW3TIeAT8ezYGDxOOrQ7zr"
#         }
#     }
#     context = {}
#     response = lambda_handler(event, context)
#     print(response)
