from datetime import datetime
from pytz import timezone
from base.response import (
    ErrorMessage, ErrorCode, ResponseType, UserException, return_response
)
from models import UserModel
from queries import UserQuery


class UserService:
    def __init__(self, user: UserModel):
        self.user = user

    def validate_user(self, raise_exception=False):
        is_valid = not (self.user is None or self.user.is_active is False)
        if raise_exception and not is_valid:
            raise UserException(message=ErrorMessage.USER_NOT_FOUND, err_code=ErrorCode.USER_NOT_FOUND)

        return is_valid

    def validate_end_paid_datetime(self):
        if (
            self.user.end_paid_datetime is None
            or self.user.end_paid_datetime < datetime.now().astimezone(timezone('UTC'))
        ):
            return False

        return True

    def validate_total_member(self, add_member=False, raise_exception=False):
        is_valid = True
        if add_member:
            if self.user.total_member >= self.user.max_member:
                is_valid = False
        else:
            if self.user.total_member > self.user.max_member:
                is_valid = False

        if is_valid is False and raise_exception:
            raise UserException(message=ErrorMessage.USER_EXCEEDED, err_code=ErrorCode.USER_EXCEEDED)

        return is_valid

    def update_sale_method(self, sale_method: str, sale_method_description: str):
        user_data = {
            "sale_method": sale_method,
            "sale_method_description": sale_method_description,
        }
        UserQuery().update_by_id(self.user.hs_hub_id, user_data)

    def increase_total_member(self, quantity: int = 1):
        user_data = {
            "total_member": self.user.total_member + quantity,
        }
        UserQuery().update_by_id(self.user.hs_hub_id, user_data)

    def decrease_total_member(self, quantity: int = 1):
        user_data = {
            "total_member": self.user.total_member - quantity,
        }
        UserQuery().update_by_id(self.user.hs_hub_id, user_data)

    def reset_data(self, end_paid_datetime, max_member=None):
        updated_data = {
            "is_reset_data": True,
            "total_member": 0,
        }

        if end_paid_datetime < datetime.now().astimezone(timezone('UTC')):
            updated_data["is_active"] = False
            updated_data["max_member"] = 0
        else:
            updated_data["end_paid_datetime"] = end_paid_datetime

        if max_member:
            updated_data["max_member"] = max_member

        UserQuery().update_by_id(self.user.hs_hub_id, updated_data)

    def remove_is_reset_data(self):
        updated_data = {
            "is_reset_data": False
        }
        UserQuery().update_by_id(self.user.hs_hub_id, updated_data)
