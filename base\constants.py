DEFAULT_NUM_OF_MEMBERS = 2
MAX_BUDGET = 5  # unit: usd, per member

SALE_METHODOLOGY = {
    "bant": "BANT",
    "spin": "SPIN Selling",
    "neat": "N.E.A.T. Selling™",
    "conceptual": "Conceptual Selling",
    "snap": "SNAP Selling",
    "challenger": "Challenger Sale",
    "sandler": "The Sandler System",
    "meddic": "MEDDIC",
    "solution": "Solution Selling",
    "inbound": "Inbound Selling",
    "target": "Target Account Selling",
    "command": "Command of the Sale",
    "gap": "Gap Selling",
}


class ActionType:
    ACCOUNTS_FETCH = "ACCOUNTS_FETCH"
    IFRAME_FETCH = "IFRAME_FETCH"
    TOGGLE_FETCH = "TOGGLE_FETCH"
    TOGGLE_UPDATE = "TOGGLE_UPDATE"


class StripeWebhookType:
    INVOICE_PAID = "invoice.paid"
    CUSTOMER_SUB_UPDATED = "customer.subscription.updated"
    CUSTOMER_SUB_DELETED = "customer.subscription.deleted"
