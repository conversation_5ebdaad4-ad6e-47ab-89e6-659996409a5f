Resources:
  LiteLLMServer:
    Type: AWS::EC2::Instance
    Properties:
      AvailabilityZone: us-east-2a
      ImageId: ami-09efc42336106d2f2
      InstanceType: t2.micro
  LiteLLMDB:
    Type: AWS::RDS::DBInstance
    Properties:
      AllocatedStorage: 20
      Engine: postgres
      MasterUsername: litellmAdmin
      MasterUserPassword: litellmPassword
      DBInstanceClass: db.t3.micro
      AvailabilityZone: us-east-2a