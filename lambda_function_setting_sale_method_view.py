import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.response import CustomException, ErrorMessage, ResponseType, return_response
from base.utils import read_html_file, save_to_log
from managers import UserManager, MemberManager
from services import UserService, MemberService

# Load environment variables
load_dotenv()

logger = logging.getLogger("setting-sale-method-view")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()
    query_params = event["queryStringParameters"]

    try:
        logger.info(query_params)
        # app_id = query_params.get("app_id")
        portal_id = query_params.get("portal_id")
        user_id = query_params.get("user_id")
        # user_email = request_data.get("user_email")
        # account_id = request_data.get("account_id")

        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        # get member from db
        member_manager = MemberManager(user_service)
        hash_key = f"{portal_id}-{user_id}"
        member = member_manager.get_member(hash_key)
        logger.info(member)
        member_service = MemberService(member, user_service)
        member_service.validate_member(raise_exception=True)

        html = read_html_file("templates/setting_sale_method.html")
        html = html.replace(
            "sale-method-action",
            "{}/settings/sale_method/update/".format(os.getenv("REVBOT_URL"))
        )
        if user.sale_method:
            html = html.replace(
                f"value=\"{user.sale_method}\"",
                f"value=\"{user.sale_method}\" selected"
            )
            html = html.replace(
                "Copy and paste your sales process documentation here.",
                user.sale_method_description
            )
        # logger.info(html)
        return {
            "statusCode": 200,
            "body": html,
            "headers": {
                "Content-Type": "text/html",
            }
        }
    except CustomException as ce:
        error = True
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        err_msg = ErrorMessage.COMMON.format(err_code)
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg, html=True)
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(query_params)

        return return_response(ResponseType.EXCEPTION, html=True)
    finally:
        save_to_log(
            api="setting_sale_method_view",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=query_params,
            version="20241028-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
