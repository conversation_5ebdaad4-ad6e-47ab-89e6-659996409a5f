import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.constants import SALE_METHODOLOGY
from base.hubspot import <PERSON><PERSON>pot
from base.response import (
    ErrorCode, ErrorMessage, CustomException, return_response, ResponseType
)
from base.utils import save_to_log
from managers import <PERSON>r<PERSON><PERSON><PERSON>, MemberManager
from services import UserService

# Load environment variables
load_dotenv()

logger = logging.getLogger("crm-card")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()
    query_params = event["queryStringParameters"]

    try:
        logger.info("20241028-001")
        portal_id = query_params.get("portalId")
        user_id = query_params.get("userId")
        user_email = query_params.get("userEmail")
        hs_object_id = query_params.get("hs_object_id")
        logger.info(query_params)

        create_deal_summary_view = os.getenv("CREATE_DEAL_SUMMARY_VIEW_URI").format(
            revbot_url=os.getenv("REVBOT_URL"),
            user_id=user_id,
            user_email=user_email,
            portal_id=portal_id,
        )
        logger.info(create_deal_summary_view)

        # get and validate user
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        # get member from db
        member_manager = MemberManager(user_service)
        hash_key = f"{portal_id}-{user_id}"
        member = member_manager.get_member(hash_key)
        logger.info(member)
        if not member:
            hubspot = Hubspot(user.hs_access_token, user.hs_refresh_token)
            response = hubspot.get_user_detail(user_id)
            logger.info(response)
            if response["error"]:
                error = True
                err_code = ErrorCode.OTHER
                err_msg = ErrorMessage.COMMON.format(err_code)
                real_err_msg = ErrorMessage.HUBSPOT_USER_DETAIL

                return return_response(ResponseType.ERROR, message=err_msg)

            response_data = response["data"]
            member_manager.create_members_from_hubspot_data([response_data])

        data = {
            "results": [
                {
                    "objectId": hs_object_id,
                    "title": "",
                    "properties": [
                        {
                            "label": "Sale Method",
                            "dataType": "STRING",
                            "value": SALE_METHODOLOGY[user.sale_method] if user.sale_method else "",
                        },
                    ],
                }
            ],
            "primaryAction": {
                "type": "IFRAME",
                "width": 890,
                "height": 748,
                "associatedObjectProperties": [
                    "hs_object_id",
                    "hubspot_owner_id"
                ],
                "uri": create_deal_summary_view,
                "label": "Create Deal Summary"
            }
        }

        return return_response(ResponseType.SUCCESS, data=data)
    except CustomException as ce:
        error = True
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        err_msg = ErrorMessage.COMMON.format(err_code)
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg)
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(query_params)

        return return_response(ResponseType.EXCEPTION)
    finally:
        save_to_log(
            api="crm_card",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=query_params,
            version="20241028-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
