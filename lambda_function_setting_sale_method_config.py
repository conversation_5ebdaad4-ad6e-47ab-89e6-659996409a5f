import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.constants import ActionType
from base.response import (
    CustomException, ErrorMessage, ResponseType, return_response
)
from base.utils import validate_action_type, validate_app_id, save_to_log
from managers import UserManager
from services import UserService

# Load environment variables
load_dotenv()

logger = logging.getLogger("sale-method-config")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()
    query_params = event["queryStringParameters"]

    try:
        action_type = query_params.get("actionType")
        app_id = query_params.get("appId")
        portal_id = query_params.get("portalId")
        user_id = query_params.get("userId")
        user_email = query_params.get("userEmail")
        account_id = query_params.get("accountId")
        logger.info(query_params)

        if response := validate_action_type(action_type, ActionType.IFRAME_FETCH): return response  # noqa
        if response := validate_app_id(app_id): return response  # noqa)

        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        param_dict = {
            "portal_id": portal_id,
            "user_id": user_id,
            "user_email": user_email,
            "app_id": app_id,
            "account_id": account_id,
        }
        logger.info(param_dict)
        params = [f"{key}={value}" for key, value in param_dict.items()]
        query_param_str = "&".join(params)
        url = os.getenv("SETTING_SALE_METHOD_VIEW_URI").format(
            revbot_url=os.getenv("REVBOT_URL"),
            query_param_str=query_param_str,
        )
        logger.info(url)

        data = {
            "response": {
                "iframeUrl": url
            }
        }

        return return_response(ResponseType.SUCCESS, data=data)
    except CustomException as ce:
        error = True
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        err_msg = ErrorMessage.COMMON.format(err_code)
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg)
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(query_params)

        return return_response(ResponseType.EXCEPTION)
    finally:
        save_to_log(
            api="sale_method_config",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=query_params,
            version="20241028-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
