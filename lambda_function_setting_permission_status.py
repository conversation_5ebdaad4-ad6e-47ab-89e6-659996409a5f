import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.constants import ActionType
from base.response import (
    CustomException, ErrorMessage, ResponseType, return_response
)
from base.utils import validate_action_type, validate_app_id, save_to_log
from managers import <PERSON>r<PERSON><PERSON><PERSON>, MemberManager
from services import UserService

# Load environment variables
load_dotenv()

logger = logging.getLogger("setting-permission-status")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()

    query_params = event["queryStringParameters"]
    try:
        logger.info("********-001")
        logger.info(query_params)
        action_type = query_params["actionType"]
        app_id = query_params["appId"]
        portal_id = query_params["portalId"]
        account_id = query_params["accountId"]

        # validate
        if response := validate_action_type(action_type, ActionType.TOGGLE_FETCH): return response  # noqa
        if response := validate_app_id(app_id): return response  # noqa)

        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        # get member from db
        member_manager = MemberManager(user_service)
        hash_key = f"{portal_id}-{account_id}"
        member = member_manager.get_member(hash_key)
        logger.info(member)

        data = {
            "actionType": ActionType.TOGGLE_FETCH,
            "response": {
                "enabled": member.is_active,
            },
            "message": None
        }

        return return_response(ResponseType.SUCCESS, data=data)
    except CustomException as ce:
        error = True
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        err_msg = ErrorMessage.COMMON.format(err_code)
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg)
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(query_params)

        return return_response(ResponseType.EXCEPTION)
    finally:
        save_to_log(
            api="setting_permission_status",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=query_params,
            version="********-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
