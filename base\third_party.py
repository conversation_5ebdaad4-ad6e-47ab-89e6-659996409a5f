import requests


class ContentType:
    APP_JSON = "application/json"
    APP_FORM = "application/x-www-form-urlencoded"


class ThirdPartyEndpoint:
    """
    The ThirdPartyEndpoint class provides access to the Third Party API
    Exp: oauth/v1/token
    """
    # raise NotImplemented
    pass


class ThirdParty:
    DEFAULT_CONTENT_TYPE = ContentType.APP_JSON
    DOMAIN = None
    access_token = None
    refresh_token = None
    api_key = None

    def __init__(self):
        if not self.DOMAIN:
            raise Exception("No domain provided")

    def build_full_url(self, endpoint):
        # check special case for full url
        if "http" in endpoint:
            return endpoint

        url = self.DOMAIN + "/" + endpoint

        return url

    def build_http_headers(
        self, content_type=DEFAULT_CONTENT_TYPE, is_required_token=False
    ):
        headers = {"Content-Type": content_type}
        if is_required_token:
            headers["Authorization"] = "Bearer " + self.access_token

        return headers

    def update_http_headers(self, headers):
        headers["Authorization"] = "Bearer " + self.access_token

        return headers

    def execute(self, method, uri, body=None, headers=None, is_required_token=False, refresh_token=True, timeout=15):
        params = None
        data = None
        if method == "GET":
            params = body
        else:
            data = body

        # print("Executing " + method + " " + uri)
        # print(params)
        # print(data)
        # print(self.build_full_url(uri))
        if headers is None:
            headers = self.build_http_headers()
        # print(headers)
        response = requests.request(
            method=method,
            url=self.build_full_url(uri),
            params=params,
            data=data,
            headers=headers,
            timeout=timeout,
        )
        # print("*****")
        # print(response)
        if response.status_code == 401:
            if refresh_token:
                self.do_refresh_token()
                headers = self.update_http_headers(headers)
                return self.execute(method, uri, body, headers, is_required_token=is_required_token, refresh_token=False, timeout=timeout)

        response = self.format_response(response)

        return response

    def format_response(self, response):
        message = None
        # data = None

        try:
            data = response.json()
        except:
            data = response.text

        result = {
            "status_code": response.status_code,
            "error": not response.ok,
            "error_code": None,
            "message": message,
            "data": data
        }

        return result

    def get_access_token(self, **kwargs):
        raise NotImplementedError("Please Implement this method")

    def do_refresh_token(self, **kwargs):
        raise NotImplementedError("Please Implement this method")
