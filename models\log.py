import os
from pynamodb.models import Model
from pynamodb.attributes import (
    UnicodeAttribute, UTCDateTimeAttribute, NumberAttribute, BooleanAttribute,
)


class LogModel(Model):
    class Meta:
        read_capacity_units = 1
        write_capacity_units = 1
        table_name = 'logs'
        region = os.getenv('AWS_REGION_NAME')
    id = NumberAttribute(hash_key=True)
    api = UnicodeAttribute()
    error = BooleanAttribute(null=True, default=False)
    err_code = UnicodeAttribute(null=True)
    err_msg = UnicodeAttribute(null=True)
    real_err_msg = UnicodeAttribute(null=True)
    err_traceback = UnicodeAttribute(null=True)
    input = UnicodeAttribute(null=True)
    executed_time = NumberAttribute(null=True)
    created_datetime = UTCDateTimeAttribute(null=True)
    version = UnicodeAttribute(null=True)
