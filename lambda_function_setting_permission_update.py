import json
import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.constants import ActionType
from base.response import (
    CustomException, ErrorMessage, ResponseType, return_response
)
from base.utils import validate_action_type, validate_app_id, save_to_log
from managers import <PERSON>r<PERSON><PERSON><PERSON>, MemberManager
from services import UserService, MemberService

# Load environment variables
load_dotenv()

logger = logging.getLogger("setting-permission-update")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()

    body = json.loads(event["body"])

    try:
        action_type = body["actionType"]
        app_id = body["appId"]
        portal_id = body["portalId"]
        account_id = body["accountId"]
        enabled = body["enabled"]
        logger.info(body)
        if response := validate_action_type(action_type, ActionType.TOGGLE_UPDATE): return response  # noqa
        if response := validate_app_id(app_id): return response  # noqa)

        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        # get member from db
        member_manager = MemberManager(user_service)
        hash_key = f"{portal_id}-{account_id}"
        member = member_manager.get_member(hash_key)
        logger.info(member)
        member_service = MemberService(member, user_service)
        is_active = True if enabled == "true" else False

        if is_active is False:
            if member.litellm_key:
                # member_service.delete_litellm_key_from_litellm(raise_exception=True)
                # update db
                # member_service.delete_litellm_key_from_db()
                if user.total_member:
                    user_service.decrease_total_member()

        else:
            if not member.litellm_key:
                logger.info("add_litellm_key")
                member_service.add_litellm_key(raise_exception=True)

            user_service.increase_total_member()

        # update db
        member_service.update_is_active(is_active)

        data = {
            "actionType": ActionType.TOGGLE_FETCH,
            "response": {
                "enabled": is_active,
            },
            "message": None
        }

        return return_response(ResponseType.SUCCESS, data=data)
    except CustomException as ce:
        error = True
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        err_msg = ErrorMessage.COMMON.format(err_code)
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg)
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(body)

        return return_response(ResponseType.EXCEPTION)
    finally:
        save_to_log(
            api="setting_permission_update",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=body,
            version="********-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )

# print(lambda_handler({"body":"""{
#     "actionType": "TOGGLE_UPDATE",
#     "portalId": "********",
#     "userId": "********",
#     "userEmail": "<EMAIL>",
#     "appId": "3123361",
#     "accountId": "********",
#     "enabled": "true"
# }"""},{}))