import json
import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv
from pytz import timezone

from base.constants import StripeWebhookType, MAX_BUDGET
from base.litellm import LiteLLM
from base.response import (
    ErrorMessage, ErrorCode, CustomException, ResponseType, return_response
)
from base.utils import delete_litellm_keys, save_to_log
from managers import UserManager, MemberManager
from services import UserService, MemberService

# Load environment variables
load_dotenv()

logger = logging.getLogger("stripe-webhook")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()
    body = json.loads(event["body"])

    try:
        logger.info("version: 20241116-001")
        logger.info(body)
        hook_type = body.get("type")

        if not hook_type or hook_type not in [
            StripeWebhookType.CUSTOMER_SUB_UPDATED,
            StripeWebhookType.CUSTOMER_SUB_DELETED,
        ]:
            err_code = ErrorCode.OTHER
            err_msg = ErrorMessage.COMMON.format(err_code)
            real_err_msg = "Invalid hook_type"
            return return_response(ResponseType.ERROR, message=err_msg)

        subscription_id = body["data"]["object"]["id"]
        max_member = body["data"]["object"]["quantity"]
        end_paid_timestamp = body["data"]["object"]["current_period_end"]
        end_paid_datetime = datetime.fromtimestamp(end_paid_timestamp, tz=timezone('UTC'))
        logger.info(end_paid_datetime)
        logger.info(type(end_paid_datetime))

        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user_by_subscription_id(subscription_id)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        logger.info(user)
        logger.info(user.end_paid_datetime)
        logger.info(type(user.end_paid_datetime))

        lite_llm = LiteLLM()
        response = lite_llm.update_team_max_budget(
            team_id=str(user.hs_hub_id),
            max_budget=max_member * MAX_BUDGET
        )
        if response["error"]:
            error = True
            err_code = ErrorCode.OTHER
            err_msg = ErrorMessage.COMMON.format(err_code)
            real_err_msg = ErrorMessage.LITELLM_TEAM_MAX_BUDGET_UPDATE_FAIL

            return return_response(ResponseType.ERROR, message=err_msg)

        user_service.reset_data(end_paid_datetime, max_member)
        # get members from db
        member_manager = MemberManager(user_service)
        members = member_manager.get_all_member()
        litellm_keys = []

        logger.info(members)
        for member in members:
            if member.litellm_key:
                litellm_keys.append(member.litellm_key)

        logger.info(litellm_keys)
        if litellm_keys:
            logger.info("delete litellm_keys in Litellm site")
            success = delete_litellm_keys(litellm_keys)
            if success:
                logger.info(members)
                # TODO: upgrade later
                members = member_manager.get_all_member()
                for member in members:
                    logger.info("delete litellm_key in DB")
                    member_service = MemberService(member, user_service)
                    member_service.delete_litellm_key_from_db()

                user_service.remove_is_reset_data()
        else:
            user_service.remove_is_reset_data()

        data = {
            "response": {
                "hook_type": hook_type,
                "end_paid_timestamp": end_paid_timestamp,
                "max_member": max_member,
                "is_reset_data": True
            },
            "message": None
        }
        return return_response(ResponseType.SUCCESS, data=data)
    except CustomException as ce:
        error = True
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        err_msg = ErrorMessage.COMMON.format(err_code)
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg)
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()

        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(body)

        return return_response(ResponseType.EXCEPTION)
    finally:
        save_to_log(
            api="stripe_webhook",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=body,
            version="20241116-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
