import json
import os
from base.third_party import ThirdPartyEndpoint, ThirdParty, ContentType


class HubspotEndpoint(ThirdPartyEndpoint):
    GET_ACCESS_TOKEN = "oauth/v1/token"
    REFRESH_TOKEN = "oauth/v1/token"
    GET_INFO_BY_ACCESS_TOKEN = "oauth/v1/access-tokens/{access_token}"
    GET_USER_LIST_FROM_CRM = "crm/v3/objects/users"
    GET_USER_LIST = "settings/v3/users"
    GET_USER_DETAIL = "settings/v3/users"
    GET_USER_SEARCH_FROM_CRM = "crm/v3/objects/users/search"


class Hubspot(ThirdParty):
    DOMAIN = 'https://api.hubapi.com'
    access_token = None
    refresh_token = None

    def __init__(self, access_token=None, refresh_token=None):
        super().__init__()
        self.client_id = os.getenv("HUBSPOT_CLIENT_ID")
        self.client_secret = os.getenv("HUBSPOT_CLIENT_SECRET")
        self.access_token = access_token
        self.refresh_token = refresh_token

    def get_access_token(self, code, redirect_uri):
        method = "POST"
        uri = HubspotEndpoint.GET_ACCESS_TOKEN
        body = {
            "grant_type": "authorization_code",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "redirect_uri": redirect_uri,
            "code": code,
        }
        headers = self.build_http_headers(
            content_type=ContentType.APP_FORM
        )

        response = self.execute(method, uri, body, headers)
        # print(response)
        # print(response.json())
        if response["error"] is False:
            response_data = response["data"]
            self.access_token = response_data["access_token"]
            self.refresh_token = response_data["refresh_token"]

        return response

    def do_refresh_token(self):
        method = "POST"
        uri = HubspotEndpoint.REFRESH_TOKEN
        body = {
            "grant_type": "refresh_token",
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "refresh_token": self.refresh_token,
        }
        headers = self.build_http_headers(
            content_type=ContentType.APP_FORM
        )

        response = self.execute(method, uri, body, headers, refresh_token=False)
        # print(response)
        # print(response.json())
        if response["status_code"] == 200:
            response_data = response["data"]
            self.access_token = response_data["access_token"]
            self.refresh_token = response_data["refresh_token"]
            # print('vao day')
        return response

    def get_info_by_access_token(self):
        method = "GET"
        uri = HubspotEndpoint.GET_INFO_BY_ACCESS_TOKEN.format(
            access_token=self.access_token
        )
        response = self.execute(method, uri)
        # print(response)
        # print(response.json())
        return response

    def get_user_list_from_crm(self):
        method = "GET"
        headers = self.build_http_headers(is_required_token=True)
        properties = [
            # "hs_job_title",
            # "hs_additional_phone",
            "hs_access",
            "hs_email",
            "hs_family_name",
            "hs_given_name",
            "hs_internal_user_id",
            # "hs_is_partner_user",
            # "hs_manager",
            # "hs_object_id",
            # "hubspot_owner_id"
        ]
        uri = HubspotEndpoint.GET_USER_LIST_FROM_CRM + "?properties=" + ",".join(properties)
        response = self.execute(method, uri, headers)
        # print(response)
        return response

    def get_user_search_from_crm_by_hs_internal_user_id(self, hs_user_id):
        method = "POST"
        headers = self.build_http_headers(is_required_token=True)

        body = {
            "filterGroups": [
                {
                    "filters": [
                        {
                            "propertyName": "hs_internal_user_id",
                            "operator": "EQ",
                            "value": str(hs_user_id)
                        }
                    ]
                }
            ],
            "properties": [
                "hs_access",
                "hs_additional_phone",
                "hs_created_by_user_id",
                "hs_email",
                "hs_family_name",
                "hs_given_name",
                "hs_internal_user_id",
                "hs_is_partner_user",
                "hs_job_title",
                "hs_manager",
                "hubspot_owner_id"
            ]
        }
        # print(body)
        body = json.dumps(body)
        uri = HubspotEndpoint.GET_USER_SEARCH_FROM_CRM
        response = self.execute(method, uri, body, headers)
        # print(response)
        return response

    def get_user_list(self):
        method = "GET"
        headers = self.build_http_headers(is_required_token=True)

        uri = HubspotEndpoint.GET_USER_LIST
        response = self.execute(method, uri, headers)
        # print(response)
        return response

    def get_user_detail(self, hs_user_id):
        method = "GET"
        headers = self.build_http_headers(is_required_token=True)
        uri = HubspotEndpoint.GET_USER_DETAIL + "/" + str(hs_user_id)
        response = self.execute(method, uri, headers)
        # print(response)
        return response

    def format_response(self, response):
        result = super().format_response(response)
        # print(result)
        if result["error"] is True:
            result["error_code"] = result["data"].get("status")
            result["message"] = result["data"].get("message")

        return result
