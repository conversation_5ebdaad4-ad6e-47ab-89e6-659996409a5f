<html>

<head>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Oswald:wght@200..700&display=swap" rel="stylesheet">
    <script src="https://code.jquery.com/jquery-3.7.1.slim.js"
        integrity="sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=" crossorigin="anonymous"></script>
    <script type="text/javascript" src="http://ajax.googleapis.com/ajax/libs/jqueryui/1.11.1/jquery-ui.min.js"></script>
    <style>
        img {
            float: left;
            margin-right: 20px;
        }

        .tg {
            border-collapse: collapse;
            border-spacing: 0;
            background: #f4f4f4;
            border-radius: 12px;
            padding: 20px;
        }

        .tg td {
            border-color: white;
            border-style: none;
            border-width: 1px;
            font-family: Arial, sans-serif;
            font-size: 16px;
            overflow: hidden;
            padding: 10px 5px;
            word-break: normal;
        }

        .tg th {
            border-color: white;
            border-style: none;
            border-width: 1px;
            font-family: Arial, sans-serif;
            font-size: 16px;
            font-weight: normal;
            overflow: hidden;
            padding: 10px 5px;
            word-break: normal;
        }

        .tg .tg-0lax {
            text-align: left;
            vertical-align: top;
        }

        .button-container {
            text-align: center;
        }

        .button-15 {
            background-image: linear-gradient(#42a1ec, #0070c9);
            border: 1px solid #0077cc;
            border-radius: 4px;
            box-sizing: border-box;
            color: #ffffff;
            cursor: pointer;
            direction: ltr;
            display: block;
            font-family: "SF Pro Text", "SF Pro Icons", "AOS Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-size: 17px;
            font-weight: 400;
            letter-spacing: -0.022em;
            line-height: 1.47059;
            min-width: 30px;
            overflow: visible;
            padding: 4px 15px;
            text-align: center;
            vertical-align: baseline;
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            white-space: nowrap;
            margin: 5px;
        }

        .button-15:disabled {
            cursor: default;
            opacity: 0.3;
        }

        .button-15:hover {
            background-image: linear-gradient(#51a9ee, #147bcd);
            border-color: #1482d0;
            text-decoration: none;
        }

        .button-15:active {
            background-image: linear-gradient(#3d94d9, #0067b9);
            border-color: #006dbc;
            outline: none;
        }

        .button-15:focus {
            box-shadow: rgba(131, 192, 253, 0.5) 0 0 0 3px;
            outline: none;
        }

        .button-16 {
            background-image: linear-gradient(#42a1ec, #0070c9);
            border: 1px solid #0077cc;
            border-radius: 4px;
            box-sizing: border-box;
            color: #ffffff;
            cursor: pointer;
            direction: ltr;
            display: block;
            font-family: "SF Pro Text", "SF Pro Icons", "AOS Icons", "Helvetica Neue", Helvetica, Arial, sans-serif;
            font-size: 17px;
            font-weight: 400;
            letter-spacing: -0.022em;
            line-height: 1.47059;
            min-width: 30px;
            overflow: visible;
            padding: 4px 15px;
            text-align: center;
            vertical-align: baseline;
            user-select: none;
            -webkit-user-select: none;
            touch-action: manipulation;
            white-space: nowrap;
            margin: 5px;
        }

        .button-16:disabled {
            cursor: default;
            opacity: 0.3;
        }

        .button-16:hover {
            background-image: linear-gradient(#51a9ee, #147bcd);
            border-color: #1482d0;
            text-decoration: none;
        }

        .button-16:active {
            background-image: linear-gradient(#3d94d9, #0067b9);
            border-color: #006dbc;
            outline: none;
        }

        .button-16:focus {
            box-shadow: rgba(131, 192, 253, 0.5) 0 0 0 3px;
            outline: none;
        }

        #processing-message {
            padding-top: 20px;
        }

        /* HTML: <div class="loader"></div> */
        .loader {
            margin: auto;
            width: 50px;
            --b: 8px;
            aspect-ratio: 1;
            border-radius: 50%;
            background: #514b82;
            -webkit-mask:
                repeating-conic-gradient(#0000 0deg, #000 1deg 70deg, #0000 71deg 90deg),
                radial-gradient(farthest-side, #0000 calc(100% - var(--b) - 1px), #000 calc(100% - var(--b)));
            -webkit-mask-composite: destination-in;
            mask-composite: intersect;
            animation: l5 1s infinite;
        }

        @keyframes l5 {
            to {
                transform: rotate(.5turn)
            }
        }

        #the-count {
            float: right;
            padding: 0.1rem 0 0 0;
            font-size: 0.875rem;
        }
    </style>
    <script>
        $(document).ready(function () {
            const portalId = new URLSearchParams(window.location.search).get('portal_id');
            console.log(portalId);
            document.getElementById("portalId").value = portalId;
            const userId = new URLSearchParams(window.location.search).get('user_id');
            console.log(userId);
            document.getElementById("userId").value = userId;
            const userEmail = new URLSearchParams(window.location.search).get('user_email');
            console.log(userEmail);
            document.getElementById("userEmail").value = userEmail;
            const appId = new URLSearchParams(window.location.search).get('app_id');
            console.log(appId);
            document.getElementById("appId").value = appId;
            const accountId = new URLSearchParams(window.location.search).get('account_id');
            console.log(accountId);
            document.getElementById("accountId").value = accountId;
            // const sale_method =  new URLSearchParams(window.location.search).get('sale_method');
            // console.log(sale_method);
            // document.getElementById("method").value = sale_method;
            // const sale_method_description =  new URLSearchParams(window.location.search).get('sale_method_description');
            // console.log(sale_method_description);
            // document.getElementById("sales_process").value = sale_method_description;
            var characterCount = $('textarea').val().length;
            var current = $('#current');
            current.text(characterCount);

            $('textarea').keyup(function () {
                var characterCount = $(this).val().length;
                var current = $('#current');

                current.text(characterCount);
            });

            document.querySelector("#methodForm").addEventListener("submit", function (e) {
                window.parent.postMessage(JSON.stringify({ "action": "DONE" }), "*");
            });
        });
    </script>
</head>

<body>
    <div style="box-shadow: 0px 0px 4px #dddddd;
            border-top: 0px; border-color: #dddddd;
            border-style: solid;
            border-left: 0px;
            border-right: 0px;
            border-bottom: 0px;padding-left: 41px;
            padding-top: 14px; width:822px;">
        <div>
            <img src="https://revbot.s3.ap-southeast-1.amazonaws.com/RevBot.png" alt="RevBot Icon" width="200"
                style="width: 110px;">
            <h2
                style="font-family: 'Oswald', sans-serif;  font-optical-sizing: auto;  font-weight: 800;  font-style: normal; padding-top: 8px;">
                REVBOT: Admin Settings</h2>
        </div>
        <form action="sale-method-action" style="padding-top: 64px; padding-bottom:41px;" id="methodForm" method="POST">
            <div id="checkboxes">
                <table class="tg">
                    <tbody>
                        <input type="hidden" id="portalId" name="portalId" value="">
                        <input type="hidden" id="userId" name="userId" value="">
                        <input type="hidden" id="userEmail" name="userEmail" value="">
                        <input type="hidden" id="appId" name="appId" value="">
                        <input type="hidden" id="accountId" name="accountId" value="">
                        <tr>
                            <td class="tg-0lax">
                                <label for="method">Choose a Methodology:</label>
                                <select name="method" id="method">
                                    <option value="bant">BANT</option>
                                    <option value="spin">SPIN Selling</option>
                                    <option value="neat">
                                        N.E.A.T. Selling™
                                    </option>
                                    <option value="conceptual">
                                        Conceptual Selling
                                    </option>
                                    <option value="snap">SNAP Selling</option>
                                    <option value="challenger">
                                        Challenger Sale
                                    </option>
                                    <option value="sandler">
                                        The Sandler System
                                    </option>
                                    <option value="meddic">MEDDIC</option>
                                    <option value="solution">
                                        Solution Selling
                                    </option>
                                    <option value="inbound">
                                        Inbound Selling
                                    </option>
                                    <option value="target">
                                        Target Account Selling
                                    </option>
                                    <option value="command">
                                        Command of the Sale
                                    </option>
                                    <option value="gap">Gap Selling</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td class="tg-0lax">
                                <textarea id="sales_process" name="sales_process" rows="30" cols="100" maxlength="8000">
Copy and paste your sales process documentation here.</textarea>
                            </td>
                        </tr>
                        <tr>
                            <td class="tg-0lax">
                                <div id="the-count">
                                    <span id="current">0</span>
                                    <span id="maximum">/ 8000</span>
                                </div>
                            </td>
                            <td class="tg-0lax"></td>
                        </tr>
                        <tr>
                            <td class="tg-0lax">
                                <input class="button-15" role="button" type="submit" value="Submit RevBot Settings" />
                            </td>
                            <td class="tg-0lax"></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </form>
    </div>
</body>

</html>