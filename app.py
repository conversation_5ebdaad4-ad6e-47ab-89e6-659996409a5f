import json
import requests
import os
import click
import codecs
import stripe
from uuid import uuid4
from datetime import datetime
from pytz import timezone
from flask import Flask, request, redirect
from base.hubspot import <PERSON><PERSON><PERSON>
from base.litellm import LiteLLM
from base.constants import SALE_METHODOLOGY, MAX_BUDGET
from base.utils import generate_litellm_key_v2
from queries import UserQuery, LogQuery, MemberQuery
from managers import UserManager, MemberManager
from models import UserModel, LogModel, MemberModel
from services import UserService, MemberService, UserException

app = Flask(__name__)


@app.cli.command("delete-table")
@click.argument("table_name")
def delete_table(table_name):
    if table_name == UserModel.Meta.table_name:
        if UserModel.exists():
            print(UserModel.delete_table())

    if table_name == LogModel.Meta.table_name:
        if LogModel.exists():
            print(LogModel.delete_table())


@app.cli.command("create-table")
@click.argument("table_name")
def create_table(table_name):
    if table_name == UserModel.Meta.table_name:
        if not UserModel.exists():
            UserModel.create_table(wait=True)

    if table_name == MemberModel.Meta.table_name:
        if not MemberModel.exists():
            MemberModel.create_table(wait=True)

    if table_name == LogModel.Meta.table_name:
        if not LogModel.exists():
            LogModel.create_table(wait=True)


@app.cli.command("test-command")
@click.argument("function")
def test_command(function):
    print(function)
    if function == "lite_llm_create_team":
        hs_hub_id = 46789870
        lite_llm = LiteLLM()
        response = lite_llm.create_team(team_id=str(hs_hub_id))
        print(response)
        if response["error"]:
            litellm_team_id = None
        else:
            litellm_team_id = response["data"]["team_id"]
    if function == "get_user_search_from_crm_by_hs_internal_user_id":
        portal_id = 46789870
        user_id = 68982642
        print(portal_id)
        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        print(user)
        # logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        # get member from db
        member_manager = MemberManager(user_service)
        hash_key = f"{portal_id}-{user_id}"
        member = member_manager.get_member(hash_key)
        print(member)
        hubspot = Hubspot(user.hs_access_token, user.hs_refresh_token)
        # response = hubspot.get_user_detail(user_id)
        # print(response)
        response = hubspot.get_user_search_from_crm_by_hs_internal_user_id(user_id)
        print(response)
        hs_access = response["data"]["results"][0]["properties"]["hs_access"]
        print(hs_access)
    if function == "generate_litellm_key_v2":
        litellm_key = generate_litellm_key_v2("********-********", litellm_team_id="********")
        print(litellm_key)

    if function == "datetime":
        print(datetime.utcnow())
        print(datetime.now().astimezone(timezone('UTC')))
    if function == "litellm_key_generate":
        lite_llm = LiteLLM()
        response = lite_llm.generate_key(user_id_str="12345", max_budget=None)
        print(response)
        print(response["data"]["key"])
    if function == "store_member":
        user = UserQuery().retrieve_user(hs_hub_id=46952881)
        hubspot = Hubspot(user.hs_access_token, user.hs_refresh_token)
        response = hubspot.get_user_list()
        print(response)
        # if response["error"]:
        #     return {
        #         "statusCode": response["status_code"],
        #         "body": json.dumps(response),
        #     }
        response_data = response["data"]["results"]
        MemberService().create_members_from_hubspot_data(user, response_data)
    if function == "store_member_litellm_key":
        portal_id = 46952755
        user_id = ********
        # get and validate user
        user = UserQuery().get_by_id(int(portal_id))
        print("user")
        print(user)
        if response := validate_user(user,error_page=True): return response  # noqa
        # get and validate member
        member_id = f"{portal_id}-{user_id}"
        member = MemberQuery().get_by_id(member_id)
        print("member")
        print(member)
        if not member:
            member = MemberService().create_member(user, user_id)
        print("member")
        print(member)
        if member.is_active and member.litellm_key is None:
            member = MemberService().add_litellm_key(member, max_budget=user.max_budget)
        print("member")
        print(member)
        if response := validate_member(member, error_page=True): return response  # noqa
        print(1)
        if response := validate_budget(member, error_page=True): return response  # noqa

    if function == "validate_budget":
        user = UserQuery().retrieve_user(hs_hub_id=46952881)
        validate_budget(user)
    if function == "get_member_list":
        members = MemberQuery().get_list(hs_hub_id=46952755)
        print(members)
        for member in members:
            print(member)

    if function == "update_litellm":
        portal_id = 46952755
        user = UserQuery().get_by_id(int(portal_id))
        members = MemberQuery().get_list(hs_hub_id=user.hs_hub_id)
        # print(members)
        # for member in members:
        #     print(member)
        #     if member.is_active:
        #         member_data = {
        #             "max_budget": MAX_BUDGET,
        #             "is_reset_data": True
        #         }
        #         MemberQuery().update_by_id(member.id, member_data)

        has_error = False
        for member in members:
            print('vao day 2')
            if member.is_active:
                success = MemberService().update_max_budget(member,
                                                            max_budget=MAX_BUDGET,
                                                            reset_spend=True)
                print(success)
                if success:
                    member_data = {"is_reset_data": False}
                    MemberQuery().update_by_id(member.id, member_data)
                else:
                    has_error = True

        if has_error is False:
            user_data = {"is_reset_data": False}
            UserQuery().update_by_id(user.hs_hub_id, user_data)
    if function == "stripe_get_subscription":
        stripe.api_key = "sk_test_51PSgVBBuk4DyGINqSGOZbDSoSvoOYM6gang3HWeQgFxHmz8hEB8HswKRs5DCEdingV9pRm7zZJasbC1jol7gzYFn00wyE3LgjP"
        subscription_id = "sub_1PxOX0Buk4DyGINqPV6sp44d"
        try:
            # Retrieve the subscription details from Stripe
            subscription = stripe.Subscription.retrieve(subscription_id)
            print(subscription)
            # # Extract the subscription end time
            # current_period_end = subscription.get('current_period_end')
            #
            # if current_period_end:
            #     # Convert the timestamp to a datetime object
            #     end_time = datetime.fromtimestamp(current_period_end,
            #                                       tz=timezone('UTC'))
            #     return end_time
            # else:
            #     return None
        except stripe.error.StripeError as e:
            print(f"Error retrieving subscription: {e}")
            return None
    if function == "member_create":
        user = UserQuery().retrieve_user(hs_hub_id=********)
        print(user)
        user_service = UserService(user)
        try:
            user_service.validate_total_member(raise_exception=True)
        except UserException as e:
            print(1)
            print(type(e))
            print(str(e))
        except Exception as e:
            print(2)
            print(type(e))
            print(str(e))
    if function == "validate_user":
        portal_id = ********
        print(portal_id)
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        print(user)
        user_service = UserService(user)
        # user = UserQuery().get_by_id(int(portal_id))
        if response := user_service.return_fail_validate_user_response(): return response  # noqa
        print(1234)
    if function == "get_all_member":
        portal_id = ********
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        print(user)
        user_service = UserService(user)
        member_manager = MemberManager(user_service)
        members = member_manager.get_all_member_info_for_setting()
        print(members)


@app.route("/oauth-callback")
def auth():
    code = request.args.get('code')
    # print(code)
    # get access token
    redirect_uri = os.getenv("OAUTH_CALLBACK_URI")
    hubspot = Hubspot()
    response = hubspot.get_access_token(code, redirect_uri)
    # print(response)
    # print(type(response))
    if response["error"]:
        return response, response["status_code"]

    response_data = response["data"]
    access_token = response_data["access_token"]
    refresh_token = response_data["refresh_token"]

    # get user info
    hubspot = Hubspot(access_token, refresh_token)
    response = hubspot.get_info_by_access_token()
    # print(response)
    if response["error"]:
        return response, response["status_code"]

    response_data = response["data"]
    hs_hub_id = response_data["hub_id"]

    # get and validate user
    user = None
    try:
        user = list(UserQuery().get_user(hs_hub_id))[0]
    except:
        pass

    # if user:

    # create user
    if user is None:
        litellm_key = generate_litellm_key(hs_hub_id)
        if not litellm_key:
            raise Exception("Could not generate litellm key")

        user_data = {
            "hs_hub_id": hs_hub_id,
            "hs_hub_domain": response_data["hub_domain"],
            "hs_user_id": response_data["user_id"],
            "hs_user_email": response_data["user"],
            "hs_access_token": access_token,
            "hs_refresh_token": refresh_token,
            "created_datetime": datetime.utcnow(),
            "litellm_key": litellm_key,
        }
        # print(user_data)
        UserQuery().create_user(user_data)
    else:
        user_data = {
            "hs_access_token": access_token,
            "hs_refresh_token": refresh_token,
            "hs_user_email": response_data["user"],
        }

        if not user.litellm_key:
            litellm_key = generate_litellm_key(hs_hub_id)
            user_data["litellm_key"] = litellm_key

        UserQuery().update_user(hs_hub_id, user_data)

    user = list(UserQuery().get_user(hs_hub_id))[0]
    # print(user)
    # response_data = {
    #     "hs_hub_id": user.hs_hub_id,
    #     "hs_hub_domain": user.hs_hub_domain,
    #     "hs_user_id": user.hs_user_id,
    #     "hs_user_email": user.hs_user_email,
    # }
    # return response_data
    print(user.end_paid_datetime)
    print(type(user.end_paid_datetime))
    print(datetime.now().astimezone(timezone('UTC')))
    print(type(datetime.now().astimezone(timezone('UTC'))))
    if not user.end_paid_datetime or user.end_paid_datetime < datetime.now().astimezone(timezone('UTC')):
        return redirect(f"https://scovy.io/price-table/", code=302)

    return redirect(f"https://app.hubspot.com/integrations-settings/{user.hs_hub_id}/installed", code=302)


@app.route("/create-deal-summary", methods=["POST"])
def create_deal_summary():
    url = os.getenv("OPEN_AI_URL") + "/process_interactions"
    # print(request.args)
    activities = request.form.getlist('activities')
    print(request.form)
    request_body = request.form.to_dict()
    print(request_body)
    print(activities)
    hubspot_owner_id = request_body.get("hubspot_owner_id")
    hs_object_id = request_body.get("hs_object_id")
    portal_id = request_body.get("portalId")
    print(hubspot_owner_id)
    print(hs_object_id)
    print(url)

    hs_hub_id = int(portal_id)
    user = None
    try:
        user = list(UserQuery().get_user(hs_hub_id))[0]
    except:
        pass

    if user is None:
        return {"error": True, "message": "You do not have a RevBot license assigned, please contact your HubSpot administrator for more information."}, 400

    response = requests.request(
        "POST",
        url,
        data=json.dumps({
            "model_name": "gpt-3.5-turbo",
            "hubspot_owner_id": hubspot_owner_id,
            "hs_object_id": hs_object_id,
            "litellm_key": user.litellm_key,
            "portal_id": user.hs_hub_id,
            "app_id": os.getenv("HUBSPOT_APP_ID"),
            "activities": activities,
            "sale_method": user.sale_method,
        }),
        headers={"content-type": "application/json"},
        timeout=60,
    )
    print(response)
    print(response.json())
    if response.status_code == 200:
        return {"success": True}

    return {"success": False}


@app.route("/create-deal-summary-view")
def get_create_deal_summary_view():
    file = codecs.open("templates/flask/create_deal_summary_flask.html", 'r', "utf-8")
    html = file.read()
    # print(html)
    return html


@app.route("/settings/crm-card")
def get_crm_card():
    hubspot_owner_id = request.args.get("hubspot_owner_id")
    hs_object_id = request.args.get("hs_object_id")
    portal_id = request.args.get("portalId")
    user_id = request.args.get("userId")
    user_email = request.args.get("userEmail")
    print(request.args)
    create_deal_summary_view = os.getenv("REVBOT_URL") + f"/create-deal-summary-view?user_id={user_id}&user_email={user_email}&portal_id={portal_id}"

    hs_hub_id = int(portal_id)
    user = None
    try:
        user = list(UserQuery().get_user(hs_hub_id))[0]
    except:
        pass

    if user is None:
        return {"error": True, "message": "You do not have a RevBot license assigned, please contact your HubSpot administrator for more information."}, 400

    return {
       "results": [
          {
             "objectId": hs_object_id,
             "title": "",
             # "link": "https://example.com/2",
             "properties": [
                {
                   "label": "Sale Method",
                   "dataType": "STRING",
                   "value": SALE_METHODOLOGY[user.sale_method] if user.sale_method else "",
                },
             ],
          }
       ],
       "primaryAction": {
            "type": "IFRAME",
            "width": 890,
            "height": 748,
            "associatedObjectProperties": [
                "hs_object_id",
                "hubspot_owner_id"
            ],
            "uri": create_deal_summary_view,
            "label": "Create Deal Summary"
       }
    }


@app.route("/accounts/list/")
def get_account_list():
    # print(request.args)
    # user_email = request.args.get("userEmail")
    portal_id = request.args.get("portalId")
    # user_id = request.args.get("userId")
    action_type = request.args.get("actionType")
    app_id = request.args.get("appId")

    if not action_type or action_type != "ACCOUNTS_FETCH":
        return {"error": True, "message": "Invalid actionType"}, 400
    if not app_id or app_id != os.getenv("HUBSPOT_APP_ID"):
        return {"error": True, "message": "Invalid appId"}, 400

    # get users
    hs_hub_id = int(portal_id)
    # print(hs_hub_id)
    user = list(UserQuery().get_user(hs_hub_id))[0]
    # print(user)
    # get user info
    hubspot = Hubspot(user.hs_access_token, user.hs_refresh_token)
    response = hubspot.get_user_list()
    accounts = []
    for e in response["data"].get("results", []):
        accounts.append({
            "accountId": e["properties"]["hs_email"],
            "accountName": e["properties"]["hs_given_name"] + " " + e["properties"]["hs_family_name"],
            # "accountLogoUrl": None
        })

    if user.hs_access_token != hubspot.access_token:
        user_data = {"hs_access_token": hubspot.access_token}
        UserQuery().update_user(user.hs_hub_id, user_data)

    return {
        "actionType": "ACCOUNTS_FETCH",
        "response": {
            "accounts": accounts
        },
        "message": None
    }


@app.route("/settings/users")
def get_settings_users():
    # print(request.args)
    # user_email = request.args.get("userEmail")
    portal_id = request.args.get("portalId")
    # user_id = request.args.get("userId")
    action_type = request.args.get("actionType")
    app_id = request.args.get("appId")

    if not action_type or action_type != "ACCOUNTS_FETCH":
        return {"error": True, "message": "Invalid actionType"}, 400
    if not app_id or app_id != os.getenv("HUBSPOT_APP_ID"):
        return {"error": True, "message": "Invalid appId"}, 400

    # get users
    hs_hub_id = int(portal_id)
    # print(hs_hub_id)
    user = list(UserQuery().get_user(hs_hub_id))[0]
    # print(user)
    # get user info
    hubspot = Hubspot(user.hs_access_token, user.hs_refresh_token)
    response = hubspot.get_user_list()
    accounts = []
    for e in response["data"].get("results", []):
        accounts.append({
            "accountId": e["properties"]["hs_email"],
            "accountName": e["properties"]["hs_given_name"] + " " + e["properties"]["hs_family_name"],
            # "accountLogoUrl": None
        })

    if user.hs_access_token != hubspot.access_token:
        user_data = {"hs_access_token": hubspot.access_token}
        UserQuery().update_user(user.hs_hub_id, user_data)

    return {
        "actionType": "ACCOUNTS_FETCH",
        "response": {
            "accounts": accounts
        },
        "message": None
    }


@app.route("/settings/permissions/status/")
def get_setting_permissions_status():
    start_time = datetime.now()
    print(request.args)
    # account_id = request.args.get("accountId")
    portal_id = request.args.get("portalId")
    # user_id = request.args.get("userId")
    action_type = request.args.get("actionType")
    app_id = request.args.get("appId")

    if not action_type or action_type != "TOGGLE_FETCH":
        return {"error": True, "message": "Invalid actionType"}, 400
    if not app_id or app_id != os.getenv("HUBSPOT_APP_ID"):
        return {"error": True, "message": "Invalid appId"}, 400

    # get user
    hs_hub_id = int(portal_id)
    user = None
    try:
        user = list(UserQuery().get_user(hs_hub_id))[0]
    except:
        pass

    if user is None:
        return {"error": True, "message": "You do not have a RevBot license assigned, please contact your HubSpot administrator for more information."}, 400
    # print(user.grant_permission)

    LogQuery().create_log({
        "id": int(str(uuid4().int)[:15]),
        "api": "setting_permission_status",
        "executed_time": (datetime.now() - start_time).microseconds * 0.000001,
        "created_datetime": start_time,
    })

    return {
        "actionType": "TOGGLE_FETCH",
        "response": {
            "enabled": user.grant_permission,
        },
        "message": None
    }


@app.route("/settings/permissions/update/", methods=["POST"])
def update_setting_permissions_status():
    start_time = datetime.now()

    request_data = request.get_json()
    print(request_data)
    # account_id = request_data.get("accountId")
    portal_id = request_data.get("portalId")
    # user_id = request_data.get("userId")
    action_type = request_data.get("actionType")
    app_id = request_data.get("appId")
    enabled = request_data.get("enabled")

    if not action_type or action_type != "TOGGLE_UPDATE":
        return {"error": True, "message": "Invalid actionType"}, 400
    if not app_id or app_id != os.getenv("HUBSPOT_APP_ID"):
        return {"error": True, "message": "Invalid appId"}, 400

    # get user
    hs_hub_id = int(portal_id)
    user = None
    try:
        user = list(UserQuery().get_user(hs_hub_id))[0]
    except:
        pass

    if user is None:
        return {"error": True, "message": "You do not have a RevBot license assigned, please contact your HubSpot administrator for more information."}, 400

    user_data = {"grant_permission": True if enabled == "true" else False}
    UserQuery().update_user(user.hs_hub_id, user_data)

    LogQuery().create_log({
        "id": int(str(uuid4().int)[:15]),
        "api": "setting_permission_update",
        "executed_time": (datetime.now() - start_time).microseconds * 0.000001,
        "created_datetime": start_time,
    })

    return {
        "actionType": "TOGGLE_FETCH",
        "response": {
            "enabled": enabled,
        },
        "message": None
    }


@app.route("/settings/sale_method/config")
def get_setting_sale_method_config():
    """
    actionType=IFRAME_FETCH
    &portalId=********
    &userId=********
    &userEmail=<EMAIL>
    &appId=3123361
    &accountId=********
    """
    request_data = request.args
    # request_data = request.get_json()
    # print(request_data)
    action_type = request_data.get("actionType")
    portal_id = request_data.get("portalId")
    user_id = request_data.get("userId")
    user_email = request_data.get("userEmail")
    app_id = request_data.get("appId")
    print(type(app_id))
    account_id = request_data.get("accountId")

    if not action_type or action_type != "IFRAME_FETCH":
        return {"error": True, "message": "Invalid actionType"}, 400
    if not app_id or app_id != os.getenv("HUBSPOT_APP_ID"):
        return {"error": True, "message": "Invalid appId"}, 400

    # get user
    hs_hub_id = int(portal_id)
    user = None
    try:
        user = list(UserQuery().get_user(hs_hub_id))[0]
    except:
        pass

    if user is None or check_user_exists(int(user_id), user.hs_users) is False:
        return {"error": True, "message": "You do not have a RevBot license assigned, please contact your HubSpot administrator for more information."}, 400

    param_dict = {
        "portal_id": portal_id,
        "user_id": user_id,
        "user_email": user_email,
        "app_id": app_id,
        "account_id": account_id,
        "sale_method": user.sale_method,
        "sale_method_description": user.sale_method_description,
    }
    params = [f"{key}={value}" for key, value in param_dict.items()]
    param_str = "&".join(params)
    url = os.getenv("REVBOT_URL") + "/settings/sale_method/view" + "?" + param_str
    print(url)
    return {
        "response": {
            "iframeUrl": url
        }
    }


@app.route("/settings/sale_method/update/", methods=["POST"])
def update_setting_sale_method():
    start_time = datetime.now()

    # request_data = request.args
    request_data = request.form.to_dict()
    print(request_data)
    # account_id = request_data.get("accountId")
    portal_id = request_data.get("portalId")
    # user_id = request_data.get("userId")
    app_id = request_data.get("appId")
    sale_method = request_data.get("method")
    sale_method_description = request_data.get("sales_process")

    if not app_id or str(app_id) != str(os.getenv("HUBSPOT_APP_ID")):
        return {"error": True, "message": "Invalid appId"}, 400

    if not sale_method or sale_method not in SALE_METHODOLOGY.keys():
        return {"error": True, "message": "Invalid sale_method"}, 400

    # get user
    hs_hub_id = int(portal_id)
    user = None
    try:
        user = list(UserQuery().get_user(hs_hub_id))[0]
    except:
        pass

    if user is None:
        return {"error": True, "message": "You do not have a RevBot license assigned, please contact your HubSpot administrator for more information."}, 400

    user_data = {
        "sale_method": sale_method,
        "sale_method_description": sale_method_description,
    }
    UserQuery().update_user(user.hs_hub_id, user_data)

    LogQuery().create_log({
        "id": int(str(uuid4().int)[:15]),
        "api": "setting_sale_method_update",
        "executed_time": (datetime.now() - start_time).microseconds * 0.000001,
        "created_datetime": start_time,
    })

    return {
        # "actionType": "IFRAME_UPDATE",
        "response": {},
        "message": None
    }


@app.route("/settings/sale_method/view")
def get_setting_sale_method_view():
    request_data = request.args
    # print(request_data)
    portal_id = request_data.get("portal_id")
    user_id = request_data.get("user_id")

    # get user
    user = get_user(hs_hub_id=int(portal_id))
    if response := validate_user(user, error_page=True): return response  # noqa
    if response := validate_current_user(user, int(user_id), error_page=True): return response  # noqa

    file = codecs.open("templates/flask/setting_sale_method_flask.html", 'r', "utf-8")
    html = file.read()
    if user.sale_method:
        html = html.replace(f"value=\"{user.sale_method}\"", f"value=\"{user.sale_method}\" selected")
        html = html.replace("Copy and paste your sales process documentation here.", user.sale_method_description)
    # print(html)
    return html


@app.route("/stripe/webhook", methods=["POST"])
def get_stripe_webhook():
    request_data = request.get_json()
    print(request_data)
    hook_type = request_data["type"]
    user_email = None
    end_paid_time = None
    if hook_type == "invoice.paid":
        user_email = request_data["data"]["object"]["customer_email"]
        end_paid_time = request_data["data"]["object"]["lines"]["data"][0]["period"]["end"]
        print(user_email)

        # get and validate user
        user = None
        try:
            user = list(UserQuery().get_users(hs_user_email=user_email))[0]
        except:
            pass
        user_data = {
            "end_paid_datetime": datetime.fromtimestamp(end_paid_time),
        }
        UserQuery().update_user(user.hs_hub_id, user_data)

    return {
        "user_email": user_email,
    }


if __name__ == "__main__":
    app.run()
