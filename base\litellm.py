import os
import json
from dotenv import load_dotenv
from base.third_party import ThirdPartyEndpoint, ThirdParty
from base.response import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, ErrorMessage

load_dotenv()


class LiteLLMEndpoint(ThirdPartyEndpoint):
    TEAM_CREATE = "team/new"
    TEAM_UPDATE = "team/update"
    KEY_GENERATE = "key/generate"
    KEY_UPDATE = "key/update"
    KEY_INFO_V2 = "v2/key/info"
    KEY_DELETE = "key/delete"


class LiteLLM(ThirdParty):
    DOMAIN = os.getenv("LITELLM_URL")
    api_key = os.getenv("LITELLM_API_KEY")

    def __init__(self, access_token=None, refresh_token=None):
        super().__init__()
        # print(os.getenv("LITELLM_URL"))
        # print(os.getenv("LITELLM_API_KEY"))
        self.access_token = self.api_key
        if not self.access_token:
            raise LiteLLMException(real_err_mess=ErrorMessage.LITELLM_KEY_NOT_FOUND)

    def format_response(self, response):
        """
        Exp: {'detail': {'error': 'Team id = 46789870 already exists. Please use a different team id.'}}
        """
        try:
            data = response.json()
        except:
            data = response.text

        try:
            message = response.json()["detail"]["error"]
        except:
            message = None

        result = {
            "status_code": response.status_code,
            "error": not response.ok,
            "error_code": None,
            "message": message,
            "data": data
        }

        return result

    def create_team(self, team_id=None, max_budget=None):
        if not max_budget:
            raise LiteLLMException(real_err_mess=ErrorMessage.LITELLM_TEAM_MAX_BUDGET_NOT_SET)

        method = "POST"
        uri = LiteLLMEndpoint.TEAM_CREATE
        headers = self.build_http_headers(is_required_token=True)
        body = json.dumps({
            "team_alias": team_id,
            "team_id": team_id,
            "max_budget": max_budget
        })
        response = self.execute(method, uri, body, headers)
        # print(response)
        return response

    def update_team_max_budget(self, team_id=None, max_budget=None):
        if not max_budget:
            raise LiteLLMException(real_err_mess=ErrorMessage.LITELLM_TEAM_MAX_BUDGET_NOT_SET)

        method = "POST"
        uri = LiteLLMEndpoint.TEAM_UPDATE
        headers = self.build_http_headers(is_required_token=True)
        body = json.dumps({
            "team_id": str(team_id),
            "max_budget": max_budget
        })
        response = self.execute(method, uri, body, headers)
        # print(response)
        return response

    def generate_key(self, user_id_str, litellm_team_id=None, max_budget=None):
        method = "POST"
        uri = LiteLLMEndpoint.KEY_GENERATE
        headers = self.build_http_headers(is_required_token=True)
        if not litellm_team_id:
            raise LiteLLMException(real_err_mess=ErrorMessage.LITELLM_TEAM_ID_NOT_FOUND)
        body = json.dumps({
            "user_id": user_id_str,
            "key_alias": user_id_str,
            "team_id": litellm_team_id,
            "max_budget": max_budget,
        })
        response = self.execute(method, uri, body, headers)
        # print(response)
        return response

    def update_key(self, litellm_key, max_budget, reset_spend=False):
        method = "POST"
        uri = LiteLLMEndpoint.KEY_UPDATE
        headers = self.build_http_headers(is_required_token=True)
        body = {
            "key": litellm_key,
            "max_budget": max_budget,
        }
        if reset_spend:
            body["spend"] = 0

        body = json.dumps(body)
        response = self.execute(method, uri, body, headers)
        # print(response)
        return response

    def delete_key(self, litellm_key):
        method = "POST"
        uri = LiteLLMEndpoint.KEY_DELETE
        headers = self.build_http_headers(is_required_token=True)
        body = json.dumps({
            "keys": [litellm_key],
        })
        response = self.execute(method, uri, body, headers)
        # print(response)
        return response

    def delete_keys(self, litellm_keys: list):
        method = "POST"
        uri = LiteLLMEndpoint.KEY_DELETE
        headers = self.build_http_headers(is_required_token=True)
        body = json.dumps({
            "keys": litellm_keys,
        })
        response = self.execute(method, uri, body, headers)
        # print(response)
        return response

    def get_key_info_v2(self, litellm_key):
        method = "POST"
        uri = LiteLLMEndpoint.KEY_INFO_V2
        headers = self.build_http_headers(is_required_token=True)
        body = json.dumps({
            "keys": [litellm_key],
        })
        response = self.execute(method, uri, body, headers)
        # print(response)
        return response
