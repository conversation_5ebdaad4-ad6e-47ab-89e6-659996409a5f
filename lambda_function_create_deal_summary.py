import json
import logging
import os
import requests
import urllib.parse
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.response import (
    <PERSON>rrorCode, ErrorMessage, CustomException, HTTPStatusCode, ResponseType,
    return_response
)
from base.utils import save_to_log
from managers import <PERSON>r<PERSON>anager, MemberManager
from services import UserService, MemberService

# Load environment variables
load_dotenv()

logger = logging.getLogger("create-deal-summary")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()
    body = event["body"]

    try:
        logger.info(body)
        # Convert the values from lists to single values
        body = urllib.parse.parse_qs(body)
        logger.info(body)

        user_id = body.get("userId")[0]
        # user_email = body.get("userEmail")
        hs_object_id = body.get("hs_object_id")[0]
        hubspot_owner_id = body.get("hubspot_owner_id")[0]
        portal_id = body.get("portalId")[0]
        activities = body.get("activities", [])
        if isinstance(activities, str):
            activities = [activities]

        if not hs_object_id or not hubspot_owner_id:
            return return_response(
                ResponseType.ERROR,
                message="hs_object_id, hubspot_owner_id are required",
                html=True
            )

        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        # get member from db
        member_manager = MemberManager(user_service)
        hash_key = f"{portal_id}-{user_id}"
        member = member_manager.get_member(hash_key)
        logger.info(member)
        member_service = MemberService(member, user_service)
        member_service.validate_member(raise_exception=True)

        # # TODO: remove later because function should be in part: update setting permission
        # if not member.litellm_key:
        #     logger.info("add_litellm_key")
        #     member_service.add_litellm_key(raise_exception=True)
        #     user_service.increase_total_member()

        member_service.validate_budget_from_litellm(raise_exception=True)

        try:
            url = os.getenv("PROCESS_INTERACTION_URI")
            response = requests.request(
                "POST",
                url,
                data=json.dumps({
                    "model_name": "gpt-3.5-turbo",
                    "hubspot_owner_id": hubspot_owner_id,
                    "hs_object_id": hs_object_id,
                    "litellm_key": member.litellm_key,
                    "portal_id": user.hs_hub_id,
                    "app_id": os.getenv("HUBSPOT_APP_ID"),
                    "activities": activities,
                    "sale_method": user.sale_method,
                }),
                headers={"content-type": "application/json"},
                timeout=300,
            )
        except Exception as e:
            err_code = ErrorCode.OPEN_AI_FAIL
            raise e

        if response.status_code != HTTPStatusCode.HTTP_200_OK:
            error = True
            try:
                real_err_msg = response.json()["message"]
            except:
                real_err_msg = ErrorMessage.OPEN_AI_FAIL

            # {'error': True, 'message': "Error code: 400 - {'error': {'message': 'Budget has been exceeded! Current cost: 0.0501465, Max budget: 0.04', 'type': 'budget_exceeded', 'param': None, 'code': 400}}"}
            if "budget_exceeded" in response.json()["message"]:
                err_code = ErrorCode.LITELLM_BUDGET_EXCEEDED
            else:
                err_code = ErrorCode.OPEN_AI_FAIL
            err_msg = ErrorMessage.COMMON.format(err_code)
            return return_response(ResponseType.ERROR, message=err_msg, html=True)

        return return_response(html=True)
    except CustomException as ce:
        error = True
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        err_msg = ErrorMessage.COMMON.format(err_code)
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg, html=True)
    except Exception as e:
        error = True
        err_msg = str(e)
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(e))

        return return_response(ResponseType.EXCEPTION, html=True)
    finally:
        save_to_log(
            api="create_deal_summary",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=body,
            version="20241124-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
