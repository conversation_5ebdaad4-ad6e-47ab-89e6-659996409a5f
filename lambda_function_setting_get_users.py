import json
import logging
import os
import traceback
from datetime import datetime
from dotenv import load_dotenv

from base.constants import ActionType
from base.hubspot import <PERSON><PERSON>pot
from base.response import (
    ErrorCode, ErrorMessage, CustomException, ResponseType, return_response
)
from base.utils import validate_action_type, validate_app_id, save_to_log
from managers import UserManager, MemberManager
from services import UserService, MemberService

# Load environment variables
load_dotenv()

logger = logging.getLogger("setting-get-users")
logger.setLevel(logging.ERROR if os.getenv("PRODUCTION", "False") == "True" else logging.INFO)


def lambda_handler(event, context):
    error = False
    err_code = None
    err_msg = None
    real_err_msg = None
    err_traceback = None
    start_datetime = datetime.now()

    body = json.loads(event["body"])

    try:
        logger.info("********-001")
        action_type = body["actionType"]
        app_id = body["appId"]
        portal_id = body["portalId"]
        user_id = body["userId"]
        # user_email = body["userEmail"]

        if response := validate_action_type(action_type, ActionType.ACCOUNTS_FETCH): return response  # noqa
        if response := validate_app_id(app_id): return response  # noqa)

        # get user from db
        user_manager = UserManager()
        user = user_manager.get_user(int(portal_id))
        logger.info(user)
        user_service = UserService(user)
        user_service.validate_user(raise_exception=True)

        # get member from db
        member_manager = MemberManager(user_service)
        hash_key = f"{portal_id}-{user_id}"
        member = member_manager.get_member(hash_key)
        logger.info(member)
        if not member:
            hubspot = Hubspot(user.hs_access_token, user.hs_refresh_token)
            response = hubspot.get_user_detail(user_id)
            logger.info(response)
            if response["error"]:
                error = True
                err_code = ErrorCode.OTHER
                err_msg = ErrorMessage.COMMON.format(err_code)
                real_err_msg = ErrorMessage.HUBSPOT_USER_DETAIL

                return return_response(ResponseType.ERROR, message=err_msg)

            response_data = response["data"]
            member_manager.create_members_from_hubspot_data([response_data])
            member = member_manager.get_member(hash_key)

        member_service = MemberService(member, user_service)
        member_service.validate_member_settings(raise_exception=True)

        # update member role from hubspot
        hubspot = Hubspot(user.hs_access_token, user.hs_refresh_token)
        response = hubspot.get_user_search_from_crm_by_hs_internal_user_id(user_id)
        # print(response)
        if not response["error"]:
            hs_access = response["data"]["results"][0]["properties"]["hs_access"]
            is_super_admin = True if hs_access in ["super_admin", "partner_admin"] else False
            # update db
            member_service.update_is_super_admin(is_super_admin)

        accounts = member_manager.get_all_member_info_for_setting(member)
        logger.info(accounts)

        data = {
            "response": {
                "accounts": accounts,
            },
        }

        return return_response(ResponseType.SUCCESS, data=data)
    except CustomException as ce:
        error = True
        err_msg = str(ce)
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(ce))

        err_code = ce.get_err_code()
        real_err_msg = ce.get_real_err_mess()

        return return_response(ResponseType.ERROR, message=err_msg)
    except Exception as e:
        error = True
        real_err_msg = str(e)
        err_traceback = traceback.format_exc()
        logger.error(traceback.format_exc())
        logger.error(str(e))
        logger.error(body)

        return return_response(ResponseType.EXCEPTION)
    finally:
        save_to_log(
            api="setting_get_users",
            start_datetime=start_datetime,
            error=error,
            err_msg=err_msg,
            err_traceback=err_traceback,
            input_data=body,
            version="********-001",
            err_code=err_code,
            real_err_msg=real_err_msg,
        )
